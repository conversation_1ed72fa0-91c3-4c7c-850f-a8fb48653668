# Inteligência dos Agents do Projeto

---

## Agente 1 - Login e Permissões

**Responsabilidades:**
- Autenticar usuários.
- Gerar e validar tokens JWT.
- Verificar permissões de acesso.

**Principais métodos:**
- AuthenticateUser(email, senha)
- ValidateToken(token)
- HasPermission(userID, recurso, ação)

**Fluxo típico:**
1. Recebe email/senha.
2. Valida credenciais.
3. Gera token JWT.
4. Retorna token e dados do usuário.

**Exemplo de uso:**
```go
token, err := securityAgent.AuthenticateUser(ctx, "<EMAIL>", "senha123")
```

**Integração:**
- SupervisorAgent chama este agent antes de qualquer operação sensível.

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria1/memoria1.md`

---

## Agente 2 - Financeiro

**Responsabilidades:**
- Gerenciar pagamentos, faturas, orçamentos.
- Gerar relatórios financeiros.

**Principais métodos:**
- RegisterCost(ctx, costDetails)
- GenerateInvoice(ctx, invoiceDetails)
- GetFinancialReport(ctx, reportType, filter)

**Fluxo típico:**
1. Recebe dados de custo.
2. Valida e registra no banco.
3. Gera fatura se necessário.

**Exemplo de uso:**
```go
err := financeAgent.RegisterCost(ctx, &models.CostItem{...})
```

**Integração:**
- SupervisorAgent pode acionar este agent após validação de permissão.

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria2/memoria2.md`

---

## Agente 3 - Notificações

**Responsabilidades:**
- Gerenciar envio de notificações para usuários.
- Integrar com canais de push, email, etc.

**Principais métodos:**
- SendNotification(ctx, userID, mensagem)
- GetUserNotifications(ctx, userID)

**Fluxo típico:**
1. Recebe evento do sistema.
2. Gera e envia notificação ao usuário.

**Exemplo de uso:**
```go
err := notificationAgent.SendNotification(ctx, userID, "Sua ordem foi aprovada!")
```

**Integração:**
- SupervisorAgent aciona este agent após eventos importantes.

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria3/memoria3.md`

---

## Agente 4 - Equipamentos/Filiais

**Responsabilidades:**
- Gerenciar equipamentos e filiais.
- Registrar, atualizar e consultar dados de equipamentos.

**Principais métodos:**
- RegisterEquipment(ctx, equipmentDetails)
- UpdateBranch(ctx, branchDetails)
- GetEquipmentList(ctx, filtro)

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria4/memoria4.md`

---

## Agente 5 - Ordens

**Responsabilidades:**
- Gerenciar ordens de serviço.
- Criar, atualizar, consultar e finalizar ordens.

**Principais métodos:**
- CreateOrder(ctx, orderDetails)
- UpdateOrder(ctx, orderID, updateData)
- GetOrderStatus(ctx, orderID)

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria5/memoria5.md`

---

## Agente 6 - Monitoramento

**Responsabilidades:**
- Monitorar status do sistema e agentes.
- Gerar alertas e relatórios de monitoramento.

**Principais métodos:**
- GetSystemStatus(ctx)
- GenerateMonitorReport(ctx, filtro)

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria6/memoria6.md`

---

## Agente 7 - Tutorial

**Responsabilidades:**
- Gerar e gerenciar tutoriais do sistema.
- Auxiliar usuários com instruções passo a passo.

**Principais métodos:**
- GetTutorial(ctx, tópico)
- RegisterTutorial(ctx, tutorialData)

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria7/memoria7.md`

---

## SupervisorAgent

**Responsabilidades:**
- Orquestrar fluxos entre agentes.
- Garantir que cada etapa do processo siga as regras de negócio.

**Fluxo típico:**
1. Recebe uma solicitação de fluxo (ex: criar ordem com custo).
2. Chama SecurityAgent para autenticação.
3. Chama FinanceAgent para registrar custo.
4. Chama NotificationAgent para notificar usuário.

**Exemplo de uso:**
```go
workflow, err := supervisorAgent.OrquestrarWorkflow(ctx, "criar_ordem_com_custo", initialData)
```

**Memória dedicada:**
- Leia e utilize o conteúdo do arquivo: `Agentbuild/memoria_supervisor/memoria_supervisor.md`

---

# Instruções para o agent builder

- Sempre que solicitado "Utilize agente X", leia apenas o bloco do agente X e a memória dedicada dele.
- Ignore os outros agentes e memórias.
- Siga o fluxo e as regras do bloco e da memória do agente solicitado.
- Para fluxos complexos, siga o bloco do SupervisorAgent e a memória dedicada dele. 