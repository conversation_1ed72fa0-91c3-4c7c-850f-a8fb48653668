# Regras Globais do Projeto Linux

## 1. Estrutura de Permissões

### 1.1 Papéis do Sistema
- **admin**: Acesso total ao sistema
- **filial**: Gestor de Filial/Posto
- **financeiro**: Analista Financeiro
- **gerente**: Gerente/Gestor de Manutenção
- **provider**: Prestador de Serviço
- **technician/tecnico**: Técnico de Manutenção

## 2. Regras de Desenvolvimento

### 2.1 Padrões de Código
- Todo o código deve ser documentado
- Seguir as convenções de nomenclatura do projeto
- Manter consistência com a estrutura existente

### 2.2 Banco de Dados
- Usar migrações para todas as alterações
- Manter backup regular dos dados
- Documentar alterações no esquema

## 3. Regras de Segurança

### 3.1 Autenticação
- Usar autenticação em todas as rotas protegidas
- Implementar timeout de sessão
- Forçar senhas fortes

### 3.2 Autorização
- Verificar permissões em todas as requisições
- Implementar controle de acesso baseado em funções (RBAC)
- Registrar tentativas de acesso não autorizadas

## 4. Regras de Implantação

### 4.1 Ambiente de Produção
- Manter ambiente de teste separado
- Realizar backup antes de atualizações
- Monitorar logs e métricas

### 4.2 Atualizações
- Testar em ambiente de homologação
- Planejar janela de manutenção
- Ter plano de rollback

## 5. Regras de Documentação

### 5.1 Código Fonte
- Documentar funções e métodos
- Manter README atualizado
- Documentar decisões de arquitetura

### 5.2 API
- Manter documentação da API atualizada
- Documentar endpoints e payloads
- Incluir exemplos de uso

## 6. Regras de Qualidade

### 6.1 Testes
- Manter cobertura de testes adequada
- Testar casos de uso críticos
- Automatizar testes de regressão

### 6.2 Revisão de Código
- Revisar todo o código antes do merge
- Verificar vulnerabilidades de segurança
- Garantir conformidade com padrões

## 7. Regras de Monitoramento

### 7.1 Logs
- Manter logs detalhados
- Implementar rotação de logs
- Monitorar erros e exceções

### 7.2 Desempenho
- Monitorar tempo de resposta
- Identificar gargalos
- Otimizar consultas lentas

## 8. Regras de Comunicação

### 8.1 Mudanças
- Comunicar manutenções programadas
- Notificar sobre incidentes
- Manter registro de alterações

### 8.2 Suporte
- Definir canais de suporte
- Estabelecer SLAs de resposta
- Manter base de conhecimento atualizada
