# 📘 MANUAL DE CRIAÇÃO DE PÁGINAS

## 📁 1. ESTRUTURA DE ARQUIVOS

```
projeto_linux/
├── web/
│   ├── static/
│   │   ├── css/
│   │   │   └── pages/
│   │   │       └── minha_pagina.css
│   │   ├── js/
│   │   │   └── pages/
│   │   │       └── minha_pagina.js
│   │   └── img/
│   │       └── minha_pagina/
│   │           └── [imagens].{png,jpg,svg}
│   │
│   └── templates/
│       ├── components/     # Componentes reutilizáveis
│       ├── layouts/        # Layouts base
│       └── minha_pagina/   # Páginas específicas
│           ├── index.html
│           └── _partial.html  # Partes da página
│
├── internal/
│   ├── handlers/
│   │   └── minha_pagina_handler.go
│   ├── models/
│   │   └── minha_pagina.go
│   ├── repository/
│   │   └── minha_pagina_repository.go
│   ├── services/
│   │   └── minha_pagina_service.go
│   └── routes/
│       └── minha_pagina_routes.go
│
└── cmd/
    └── main.go
```

## 🛠 2. IMPLEMENTAÇÃO PASSO A PASSO

### 2.1. CRIAR O TEMPLATE HTML
```html
<!-- web/templates/minha_pagina/index.html -->
{{ define "title" }}Tí<PERSON><PERSON> da Página{{ end }}

{{ define "content" }}
<div class="container-fluid">
    <div class="page-header">
        <h1>Minha Página</h1>
    </div>
    <div class="card">
        <div class="card-body">
            <!-- Conteúdo aqui -->
        </div>
    </div>
</div>
{{ end }}

{{ define "styles" }}
<link href="/static/css/pages/minha_pagina.css" rel="stylesheet">
{{ end }}

{{ define "scripts" }}
<script src="/static/js/pages/minha_pagina.js"></script>
{{ end }}

{{ template "base" . }}
```

### 2.2. CRIAR O MODEL
```go
// internal/models/minha_pagina.go
package models

type MinhaPagina struct {
    ID        uint   `json:"id" gorm:"primaryKey"`
    Nome      string `json:"nome"`
    Descricao string `json:"descricao"`
    // Outros campos
}
```

### 2.3. CRIAR O REPOSITÓRIO
```go
// internal/repository/minha_pagina_repository.go
package repository

import (
    "gorm.io/gorm"
    "tradicao/internal/models"
)

type MinhaPaginaRepository interface {
    Create(minhaPagina *models.MinhaPagina) error
    FindByID(id uint) (*models.MinhaPagina, error)
    // Outros métodos necessários
}

type minhaPaginaRepository struct {
    db *gorm.DB
}

func NewMinhaPaginaRepository(db *gorm.DB) MinhaPaginaRepository {
    return &minhaPaginaRepository{db: db}
}

// Implementação dos métodos...
```

### 2.4. CRIAR O SERVIÇO
```go
// internal/services/minha_pagina_service.go
package services

import (
    "tradicao/internal/models"
    "tradicao/internal/repository"
)

type MinhaPaginaService interface {
    Criar(minhaPagina *models.MinhaPagina) error
    BuscarPorID(id uint) (*models.MinhaPagina, error)
    // Outros métodos necessários
}

type minhaPaginaService struct {
    repo repository.MinhaPaginaRepository
}

func NewMinhaPaginaService(repo repository.MinhaPaginaRepository) MinhaPaginaService {
    return &minhaPaginaService{repo: repo}
}

// Implementação dos métodos...
```

### 2.5. CRIAR O HANDLER
```go
// internal/handlers/minha_pagina_handler.go
package handlers

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "tradicao/internal/services"
)

type MinhaPaginaHandler struct {
    service services.MinhaPaginaService
}

func NewMinhaPaginaHandler(service services.MinhaPaginaService) *MinhaPaginaHandler {
    return &MinhaPaginaHandler{service: service}
}

func (h *MinhaPaginaHandler) Index(c *gin.Context) {
    c.HTML(http.StatusOK, "minha_pagina/index.html", gin.H{
        "title": "Minha Página",
    })
}

// Outros métodos do handler...
```

### 2.6. CRIAR AS ROTAS
```go
// internal/routes/minha_pagina_routes.go
package routes

import (
    "github.com/gin-gonic/gin"
    "tradicao/internal/handlers"
    "tradicao/internal/middleware"
)

func SetupMinhaPaginaRoutes(router *gin.RouterGroup, handler *handlers.MinhaPaginaHandler) {
    // Grupo de rotas autenticadas
    authGroup := router.Group("/minha-pagina")
    authGroup.Use(middleware.AuthRequired())
    
    // Rota principal
    authGroup.GET("", handler.Index)
    
    // Outras rotas da API
    // authGroup.POST("", handler.Criar)
    // authGroup.GET("/:id", handler.Buscar)
}
```

## 🔧 3. CONFIGURAÇÃO NO main.go

```go
// cmd/main.go
package main

import (
    // Outros imports...
    
    "tradicao/internal/handlers"
    "tradicao/internal/repository"
    "tradicao/internal/routes"
    "tradicao/internal/services"
    // Outros imports...
)

func main() {
    // Configuração inicial...
    
    // Inicializar banco de dados
    db, err := database.Connect()
    if err != nil {
        log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
    }

    // Inicializar autenticação
    authService := services.NewAuthService(db)
    
    // 1. Inicializar repositório
    minhaPaginaRepo := repository.NewMinhaPaginaRepository(db)
    
    // 2. Inicializar serviços
    minhaPaginaService := services.NewMinhaPaginaService(minhaPaginaRepo)
    
    // 3. Inicializar handlers
    minhaPaginaHandler := handlers.NewMinhaPaginaHandler(minhaPaginaService)

    // Configurar rotas
    router := gin.Default()
    
    // Middleware de autenticação
    authMiddleware := middleware.AuthMiddleware(authService)
    
    // Rotas públicas
    // ...
    
    // Rotas autenticadas
    authGroup := router.Group("/")
    authGroup.Use(authMiddleware)
    {
        // Outras rotas...
        
        // 4. Registrar rotas da nova página
        routes.SetupMinhaPaginaRoutes(authGroup, minhaPaginaHandler)
    }

    // Iniciar servidor
    port := os.Getenv("PORT")
    if port == "" {
        port = "8080"
    }
    
    log.Printf("Servidor iniciado na porta %s", port)
    if err := router.Run(":" + port); err != nil {
        log.Fatalf("Erro ao iniciar o servidor: %v", err)
    }
}
```

## 🎨 4. ARQUIVOS ESTÁTICOS

### 4.1. CSS
```css
/* web/static/css/pages/minha_pagina.css */
.minha-pagina {
    /* Estilos específicos */
}

.minha-pagina .card {
    margin-bottom: 1.5rem;
}
```

### 4.2. JAVASCRIPT
```javascript
// web/static/js/pages/minha_pagina.js
document.addEventListener('DOMContentLoaded', function() {
    // Inicialização
    initComponents();
    setupEventListeners();
});

function initComponents() {
    // Inicializar componentes (modais, tooltips, etc)
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupEventListeners() {
    // Configurar listeners de eventos
}
```

## ✅ 5. CHECKLIST FINAL

1. **Estrutura de Arquivos**:
   - [ ] Templates criados em `web/templates/`
   - [ ] Estilos em `web/static/css/pages/`
   - [ ] Scripts em `web/static/js/pages/`
   - [ ] Imagens em `web/static/img/`

2. **Código Backend**:
   - [ ] Model criado
   - [ ] Repository implementado
   - [ ] Service implementado
   - [ ] Handler criado
   - [ ] Rotas configuradas

3. **Configuração**:
   - [ ] Importações adicionadas ao `main.go`
   - [ ] Serviços inicializados
   - [ ] Rotas registradas

4. **Testes**:
   - [ ] Testar em diferentes tamanhos de tela
   - [ ] Verificar permissões
   - [ ] Testar fluxos de erro

5. **Documentação**:
   - [ ] Comentar funções complexas
   - [ ] Atualizar documentação da API
   - [ ] Atualizar README se necessário
