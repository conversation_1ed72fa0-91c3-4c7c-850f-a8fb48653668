// Configurações iniciais
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar componentes
    initComponents();
    
    // Configurar eventos
    setupEventListeners();
    
    // Carregar tutoriais iniciais
    carregarTutoriais();
});

// Inicializar componentes da página
function initComponents() {
    // Inicializar tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Configurar visualização padrão (grid)
    document.querySelector('#btnVisualizacao').classList.add('active');
}

// Configurar eventos da página
function setupEventListeners() {
    // Eventos dos botões de visualização
    document.getElementById('btnVisualizacao').addEventListener('click', function() {
        document.getElementById('gridTutoriais').classList.remove('list-view');
        this.classList.add('active');
        document.getElementById('btnLista').classList.remove('active');
    });
    
    document.getElementById('btnLista').addEventListener('click', function() {
        document.getElementById('gridTutoriais').classList.add('list-view');
        this.classList.add('active');
        document.getElementById('btnVisualizacao').classList.remove('active');
    });
    
    // Evento do botão de aplicar filtros
    document.getElementById('aplicarFiltros').addEventListener('click', carregarTutoriais);
    
    // Evento do botão de impressão no modal
    document.getElementById('btnImprimirTutorial')?.addEventListener('click', function() {
        window.print();
    });
}

// Carregar tutoriais com base nos filtros
function carregarTutoriais() {
    const container = document.getElementById('gridTutoriais');
    const semTutoriais = document.getElementById('semTutoriais');
    
    // Mostrar loading
    container.innerHTML = `
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-3">Carregando tutoriais...</p>
        </div>`;
    
    // Simular carregamento assíncrono
    setTimeout(() => {
        // Obter valores dos filtros
        const equipamento = document.getElementById('equipamento').value;
        const tipo = document.getElementById('tipo').value;
        const dificuldade = document.getElementById('dificuldade').value;
        
        // Aqui você faria uma requisição para a API
        // Por enquanto, usaremos dados de exemplo
        const tutoriais = obterTutoriaisExemplo(equipamento, tipo, dificuldade);
        
        // Atualizar a interface
        if (tutoriais.length === 0) {
            container.classList.add('d-none');
            semTutoriais.classList.remove('d-none');
        } else {
            container.classList.remove('d-none');
            semTutoriais.classList.add('d-none');
            
            // Limpar container
            container.innerHTML = '';
            
            // Adicionar tutoriais ao DOM
            tutoriais.forEach(tutorial => {
                container.appendChild(criarCardTutorial(tutorial));
            });
        }
        
        // Atualizar contagem de resultados
        atualizarContagemResultados(tutoriais.length);
    }, 800);
}

// Criar card de tutorial para a visualização em grid
function criarCardTutorial(tutorial) {
    const col = document.createElement('div');
    col.className = 'col-md-6 col-lg-4 mb-4';
    
    // Mapear níveis para classes CSS
    const niveisClasses = {
        'iniciante': 'badge-iniciante',
        'intermediario': 'badge-intermediario',
        'avancado': 'badge-avancado'
    };
    
    // Mapear ícones para tipos de tutorial
    const iconesTipos = {
        'manutencao': 'tools',
        'operacao': 'cogs',
        'seguranca': 'shield-alt',
        'default': 'book'
    };
    
    const icone = iconesTipos[tutorial.tipo] || iconesTipos['default'];
    
    col.innerHTML = `
        <div class="card tutorial-card h-100">
            <div class="card-img-top d-flex align-items-center justify-content-center bg-light">
                <i class="fas fa-${icone} text-muted" style="font-size: 3rem;"></i>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h5 class="card-title mb-0">${tutorial.titulo}</h5>
                    <span class="badge ${niveisClasses[tutorial.nivel] || 'bg-secondary'} badge-nivel">
                        ${tutorial.nivel.charAt(0).toUpperCase() + tutorial.nivel.slice(1)}
                    </span>
                </div>
                <p class="card-text text-muted small">${tutorial.descricao}</p>
                <div class="mt-auto">
                    <span class="badge bg-light text-dark mb-2">
                        <i class="fas fa-tag me-1"></i> ${tutorial.categoria}
                    </span>
                    <span class="badge bg-light text-dark mb-2">
                        <i class="far fa-clock me-1"></i> ${tutorial.duracao} min
                    </span>
                </div>
            </div>
            <div class="card-footer bg-transparent border-top-0 pt-0">
                <button class="btn btn-sm btn-outline-primary w-100" 
                        onclick="abrirModalTutorial('${tutorial.id}')">
                    <i class="fas fa-play-circle me-1"></i> Ver Tutorial
                </button>
            </div>
        </div>
    `;
    
    return col;
}

// Abrir modal com detalhes do tutorial
function abrirModalTutorial(id) {
    // Aqui você buscaria os detalhes do tutorial da API
    // Por enquanto, usaremos dados de exemplo
    const tutorial = {
        id: id,
        titulo: 'Título do Tutorial',
        descricao: 'Descrição detalhada do tutorial que será exibida no modal.',
        conteudo: `
            <h4>Introdução</h4>
            <p>Este é um exemplo de conteúdo de tutorial que seria carregado dinamicamente.</p>
            <h5 class="mt-4">Materiais Necessários</h5>
            <ul>
                <li>Ferramenta 1</li>
                <li>Material 2</li>
                <li>Equipamento 3</li>
            </ul>
            <h5 class="mt-4">Passo a Passo</h5>
            <ol>
                <li>Primeiro passo para realizar a manutenção</li>
                <li>Segundo passo com mais detalhes</li>
                <li>Terceiro e último passo para conclusão</li>
            </ol>
            <div class="alert alert-warning mt-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Atenção: Este é um tutorial de exemplo. Siga todas as normas de segurança.
            </div>
        `,
        nivel: 'intermediario',
        categoria: 'Manutenção Preventiva',
        duracao: '15',
        dataAtualizacao: '10/06/2023'
    };
    
    // Atualizar o modal com os dados do tutorial
    document.getElementById('modalTutorialLabel').textContent = tutorial.titulo;
    document.getElementById('conteudoTutorial').innerHTML = tutorial.conteudo;
    
    // Exibir o modal
    const modal = new bootstrap.Modal(document.getElementById('modalTutorial'));
    modal.show();
}

// Atualizar contagem de resultados
function atualizarContagemResultados(total) {
    const contador = document.getElementById('contadorResultados');
    if (contador) {
        contador.textContent = `${total} ${total === 1 ? 'tutorial encontrado' : 'tutoriais encontrados'}`;
    }
}

// Função de exemplo para simular dados
function obterTutoriaisExemplo(equipamento, tipo, dificuldade) {
    // Dados de exemplo
    const todosTutoriais = [
        {
            id: '1',
            titulo: 'Manutenção Básica do Equipamento A',
            descricao: 'Aprenda a realizar a manutenção preventiva básica no Equipamento A.',
            tipo: 'manutencao',
            nivel: 'iniciante',
            categoria: 'Manutenção Preventiva',
            duracao: '15',
            equipamento: 'equipamento-a'
        },
        {
            id: '2',
            titulo: 'Operação Avançada do Sistema B',
            descricao: 'Domine os recursos avançados do Sistema B com este tutorial detalhado.',
            tipo: 'operacao',
            nivel: 'avancado',
            categoria: 'Operação',
            duracao: '30',
            equipamento: 'sistema-b'
        },
        {
            id: '3',
            titulo: 'Procedimentos de Segurança',
            descricao: 'Conheça os procedimentos de segurança essenciais para operação segura.',
            tipo: 'seguranca',
            nivel: 'iniciante',
            categoria: 'Segurança',
            duracao: '10',
            equipamento: 'geral'
        },
        {
            id: '4',
            titulo: 'Solução de Problemas Comuns',
            descricao: 'Aprenda a identificar e resolver os problemas mais comuns nos equipamentos.',
            tipo: 'manutencao',
            nivel: 'intermediario',
            categoria: 'Manutenção Corretiva',
            duracao: '20',
            equipamento: 'geral'
        },
        {
            id: '5',
            titulo: 'Atualização de Firmware',
            descricao: 'Guia passo a passo para atualizar o firmware dos equipamentos.',
            tipo: 'manutencao',
            nivel: 'intermediario',
            categoria: 'Atualização de Software',
            duracao: '25',
            equipamento: 'equipamento-c'
        },
        {
            id: '6',
            titulo: 'Limpeza e Conservação',
            descricao: 'Métodos adequados para limpeza e conservação dos equipamentos.',
            tipo: 'manutencao',
            nivel: 'iniciante',
            categoria: 'Conservação',
            duracao: '12',
            equipamento: 'geral'
        }
    ];
    
    // Aplicar filtros
    return todosTutoriais.filter(tutorial => {
        const equipamentoMatch = !equipamento || tutorial.equipamento === equipamento;
        const tipoMatch = !tipo || tutorial.tipo === tipo;
        const nivelMatch = !dificuldade || tutorial.nivel === dificuldade;
        
        return equipamentoMatch && tipoMatch && nivelMatch;
    });
}
