/**
 * Script para a página de Tutoriais
 * Gerencia a listagem, filtragem e visualização de tutoriais
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elementos da página
    const tutoriaisContainer = document.getElementById('tutoriaisContainer');
    const filtroEquipamento = document.getElementById('equipamento');
    const filtroTipo = document.getElementById('tipo');
    const filtroDificuldade = document.getElementById('dificuldade');
    const btnAplicarFiltros = document.getElementById('aplicarFiltros');
    const searchInput = document.getElementById('searchInput');
    const modalTutorial = new bootstrap.Modal(document.getElementById('modalTutorial'));
    const modalTitulo = document.getElementById('modalTitulo');
    const modalConteudo = document.getElementById('modalConteudo');
    const loadingSpinner = document.getElementById('loadingSpinner');

    // Estado da aplicação
    let tutoriais = [];
    let favoritos = new Set(JSON.parse(localStorage.getItem('tutoriaisFavoritos') || '[]'));

    // Inicialização
    async function init() {
        await carregarTutoriais();
        atualizarListaTutoriais();
        atualizarContagemResultados();
        carregarEquipamentos();
        configurarEventListeners();
    }

    // Carrega a lista de tutoriais da API
    async function carregarTutoriais() {
        try {
            loadingSpinner.classList.remove('d-none');
            const response = await fetch('/api/tutoriais');
            if (!response.ok) throw new Error('Erro ao carregar tutoriais');
            tutoriais = await response.json();
        } catch (error) {
            console.error('Erro:', error);
            mostrarMensagem('Erro ao carregar tutoriais. Tente novamente mais tarde.', 'danger');
        } finally {
            loadingSpinner.classList.add('d-none');
        }
    }

    // Carrega a lista de equipamentos para o filtro
    async function carregarEquipamentos() {
        try {
            const response = await fetch('/api/equipamentos');
            if (!response.ok) throw new Error('Erro ao carregar equipamentos');
            const equipamentos = await response.json();
            
            // Limpa opções existentes (exceto a primeira)
            while (filtroEquipamento.options.length > 1) {
                filtroEquipamento.remove(1);
            }
            
            // Adiciona os equipamentos ao select
            equipamentos.forEach(equipamento => {
                const option = document.createElement('option');
                option.value = equipamento.id;
                option.textContent = equipamento.nome;
                filtroEquipamento.appendChild(option);
            });
        } catch (error) {
            console.error('Erro ao carregar equipamentos:', error);
        }
    }

    // Atualiza a lista de tutoriais exibida na página
    function atualizarListaTutoriais(filtros = {}) {
        const tutoriaisFiltrados = filtrarTutoriais(filtros);
        
        if (tutoriaisFiltrados.length === 0) {
            tutoriaisContainer.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4>Nenhum tutorial encontrado</h4>
                    <p class="text-muted">Tente ajustar os filtros ou volte mais tarde.</p>
                </div>`;
            return;
        }

        tutoriaisContainer.innerHTML = tutoriaisFiltrados.map(tutorial => `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card tutorial-card h-100">
                    <div class="position-relative">
                        <img src="${tutorial.imagemUrl || '/static/img/placeholder-tutorial.jpg'}" 
                             class="card-img-top" 
                             alt="${tutorial.titulo}">
                        <button class="favorito-btn ${favoritos.has(tutorial.id) ? 'active' : ''}" 
                                data-tutorial-id="${tutorial.id}"
                                title="${favoritos.has(tutorial.id) ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}">
                            <i class="fas fa-star"></i>
                        </button>
                        <span class="badge bg-${getBadgeColor(tutorial.dificuldade)} badge-categoria">
                            ${tutorial.dificuldade}
                        </span>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${tutorial.titulo}</h5>
                        <p class="card-text text-muted">${tutorial.descricao}</p>
                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <span class="badge bg-secondary">${tutorial.tipo}</span>
                            <button class="btn btn-sm btn-outline-primary ver-mais" 
                                    data-tutorial-id="${tutorial.id}">
                                Ver mais
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // Adiciona os event listeners aos botões
        document.querySelectorAll('.ver-mais').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tutorialId = parseInt(e.target.dataset.tutorialId);
                abrirModalTutorial(tutorialId);
            });
        });

        document.querySelectorAll('.favorito-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const tutorialId = parseInt(btn.dataset.tutorialId);
                toggleFavorito(tutorialId, btn);
            });
        });
    }

    // Filtra os tutoriais com base nos critérios fornecidos
    function filtrarTutoriais(filtros) {
        return tutoriais.filter(tutorial => {
            // Filtro por equipamento
            if (filtros.equipamento && tutorial.equipamentoId !== parseInt(filtros.equipamento)) {
                return false;
            }
            
            // Filtro por tipo
            if (filtros.tipo && tutorial.tipo.toLowerCase() !== filtros.tipo.toLowerCase()) {
                return false;
            }
            
            // Filtro por dificuldade
            if (filtros.dificuldade && tutorial.dificuldade.toLowerCase() !== filtros.dificuldade.toLowerCase()) {
                return false;
            }
            
            // Filtro por termo de busca
            if (filtros.termoBusca) {
                const busca = filtros.termoBusca.toLowerCase();
                const textoBusca = `${tutorial.titulo} ${tutorial.descricao} ${tutorial.tags || ''}`.toLowerCase();
                if (!textoBusca.includes(busca)) {
                    return false;
                }
            }
            
            return true;
        });
    }

    // Atualiza o contador de resultados
    function atualizarContagemResultados() {
        const contador = document.getElementById('contadorResultados');
        if (!contador) return;
        
        const total = document.querySelectorAll('#tutoriaisContainer .col-md-6').length;
        contador.textContent = `${total} ${total === 1 ? 'tutorial encontrado' : 'tutoriais encontrados'}`;
    }

    // Abre o modal com os detalhes do tutorial
    async function abrirModalTutorial(tutorialId) {
        try {
            const response = await fetch(`/api/tutoriais/${tutorialId}`);
            if (!response.ok) throw new Error('Tutorial não encontrado');
            
            const tutorial = await response.json();
            
            // Atualiza o modal com os dados do tutorial
            modalTitulo.textContent = tutorial.titulo;
            
            // Formata o conteúdo do tutorial
            let conteudoHTML = `
                <div class="tutorial-conteudo">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <span class="badge bg-${getBadgeColor(tutorial.dificuldade)} me-2">
                                ${tutorial.dificuldade}
                            </span>
                            <span class="badge bg-secondary">${tutorial.tipo}</span>
                        </div>
                        <div>
                            <button class="btn btn-sm ${favoritos.has(tutorial.id) ? 'btn-warning' : 'btn-outline-secondary'}" 
                                    id="btnFavoritoModal"
                                    data-tutorial-id="${tutorial.id}">
                                <i class="fas fa-star me-1"></i>
                                ${favoritos.has(tutorial.id) ? 'Favorito' : 'Adicionar aos favoritos'}
                            </button>
                        </div>
                    </div>
                    
                    <div class="tutorial-descricao mb-4">
                        ${tutorial.descricao}
                    </div>
            `;
            
            // Adiciona os passos do tutorial
            if (tutorial.passos && tutorial.passos.length > 0) {
                conteudoHTML += '<h5 class="mt-4 mb-3">Passo a Passo</h5>';
                tutorial.passos.forEach((passo, index) => {
                    conteudoHTML += `
                        <div class="passo-tutorial">
                            <h6>Passo ${index + 1}: ${passo.titulo}</h6>
                            <div class="passo-conteudo">
                                ${passo.conteudo}
                            </div>
                            ${passo.imagemUrl ? `<img src="${passo.imagemUrl}" class="img-fluid rounded mt-2" alt="Passo ${index + 1}">` : ''}
                        </div>
                    `;
                });
            }
            
            // Adiciona materiais necessários, se houver
            if (tutorial.materiais && tutorial.materiais.length > 0) {
                conteudoHTML += `
                    <div class="mt-4">
                        <h5>Materiais Necessários</h5>
                        <ul class="list-group">
                            ${tutorial.materiais.map(material => `
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    ${material.nome}
                                    ${material.quantidade ? `<span class="badge bg-primary rounded-pill">${material.quantidade}</span>` : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Adiciona dicas, se houver
            if (tutorial.dicas && tutorial.dicas.length > 0) {
                conteudoHTML += `
                    <div class="alert alert-info mt-4">
                        <h5><i class="fas fa-lightbulb me-2"></i>Dicas</h5>
                        <ul class="mb-0">
                            ${tutorial.dicas.map(dica => `<li>${dica}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            conteudoHTML += '</div>'; // Fecha a div .tutorial-conteudo
            
            modalConteudo.innerHTML = conteudoHTML;
            
            // Adiciona o event listener ao botão de favorito no modal
            const btnFavorito = document.getElementById('btnFavoritoModal');
            if (btnFavorito) {
                btnFavorito.addEventListener('click', (e) => {
                    const tutorialId = parseInt(e.target.closest('button').dataset.tutorialId);
                    toggleFavorito(tutorialId, e.target.closest('button'));
                    // Atualiza o botão no modal
                    const isFavorito = favoritos.has(tutorialId);
                    e.target.closest('button').classList.toggle('btn-warning', isFavorito);
                    e.target.closest('button').classList.toggle('btn-outline-secondary', !isFavorito);
                    e.target.nextSibling.textContent = isFavorito ? 'Favorito' : 'Adicionar aos favoritos';
                });
            }
            
            // Exibe o modal
            modalTutorial.show();
            
        } catch (error) {
            console.error('Erro ao carregar tutorial:', error);
            mostrarMensagem('Erro ao carregar o tutorial. Tente novamente mais tarde.', 'danger');
        }
    }

    // Adiciona ou remove um tutorial dos favoritos
    function toggleFavorito(tutorialId, buttonElement) {
        if (favoritos.has(tutorialId)) {
            favoritos.delete(tutorialId);
            if (buttonElement) {
                buttonElement.classList.remove('active');
                buttonElement.title = 'Adicionar aos favoritos';
            }
        } else {
            favoritos.add(tutorialId);
            if (buttonElement) {
                buttonElement.classList.add('active');
                buttonElement.title = 'Remover dos favoritos';
            }
        }
        
        // Atualiza o localStorage
        localStorage.setItem('tutoriaisFavoritos', JSON.stringify(Array.from(favoritos)));
        
        // Mostra feedback visual
        const mensagem = favoritos.has(tutorialId) 
            ? 'Tutorial adicionado aos favoritos!' 
            : 'Tutorial removido dos favoritos.';
        
        mostrarMensagem(mensagem, 'success');
    }

    // Retorna a classe CSS para o badge com base na dificuldade
    function getBadgeColor(dificuldade) {
        const cores = {
            'iniciante': 'success',
            'intermediário': 'primary',
            'intermediario': 'primary',
            'avançado': 'danger',
            'avancado': 'danger'
        };
        return cores[dificuldade.toLowerCase()] || 'secondary';
    }

    // Exibe uma mensagem para o usuário
    function mostrarMensagem(mensagem, tipo = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;
        
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${tipo} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${mensagem}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
                </div>
            </div>
        `;
        
        const toastElement = document.createElement('div');
        toastElement.innerHTML = toastHTML.trim();
        toastContainer.appendChild(toastElement.firstChild);
        
        const toast = new bootstrap.Toast(toastElement.firstChild, { autohide: true, delay: 3000 });
        toast.show();
        
        // Remove o toast do DOM após ser escondido
        toastElement.firstChild.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }

    // Configura os event listeners da página
    function configurarEventListeners() {
        // Aplicar filtros ao clicar no botão
        if (btnAplicarFiltros) {
            btnAplicarFiltros.addEventListener('click', () => {
                const filtros = {
                    equipamento: filtroEquipamento.value,
                    tipo: filtroTipo.value,
                    dificuldade: filtroDificuldade.value,
                    termoBusca: searchInput ? searchInput.value.trim() : ''
                };
                
                atualizarListaTutoriais(filtros);
                atualizarContagemResultados();
                
                // Rola para a seção de resultados
                document.getElementById('resultados').scrollIntoView({ behavior: 'smooth' });
            });
        }
        
        // Pesquisa ao pressionar Enter no campo de busca
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    btnAplicarFiltros.click();
                }
            });
        }
    }

    // Inicializa a aplicação
    init();
});
