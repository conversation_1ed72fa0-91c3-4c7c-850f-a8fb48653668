/* Estilos para a nova versão do menu lateral */

/* Seções do menu */
.nav-section {
    padding: 10px 20px;
    margin: 10px 0 5px;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.5);
    letter-spacing: 0.5px;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-section:hover {
    color: #f4b31d;
    cursor: default;
}

/* Itens do menu */
.nav-item {
    position: relative;
    margin: 2px 0;
    transition: all 0.3s ease;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.nav-item.active {
    background-color: rgba(244, 179, 29, 0.1);
    border-left: 3px solid #f4b31d;
}

.nav-item.active .nav-link {
    color: #f4b31d;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #ffffff;
    text-decoration: none;
}

.nav-icon {
    width: 24px;
    margin-right: 12px;
    text-align: center;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s ease;
}

.nav-item.active .nav-icon,
.nav-link:hover .nav-icon {
    color: #f4b31d;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Tooltips */
[data-tooltip] {
    position: relative;
    cursor: pointer;
}

[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

[data-tooltip]:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent #333;
    margin-left: 5px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

[data-tooltip]:hover::after,
[data-tooltip]:hover::before {
    opacity: 1;
    visibility: visible;
    margin-left: 15px;
}

/* Responsividade */
@media (max-width: 992px) {
    .nav-text {
        display: none;
    }
    
    .nav-icon {
        margin-right: 0;
        font-size: 18px;
    }
    
    .nav-section {
        display: none;
    }
    
    .nav-link {
        justify-content: center;
        padding: 15px 10px;
    }
    
    [data-tooltip]:hover::after,
    [data-tooltip]:hover::before {
        display: none;
    }
}
