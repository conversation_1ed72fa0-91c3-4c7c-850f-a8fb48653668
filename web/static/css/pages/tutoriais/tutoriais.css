/* Estilos para a página de Tutoriais */

/* <PERSON><PERSON><PERSON><PERSON><PERSON>ágina */
.tutoriais-page .page-header {
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.tutoriais-page .page-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

/* Cards de Tutorial */
.tutorial-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.tutorial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.tutorial-card .card-img-top {
    height: 160px;
    object-fit: cover;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 3rem;
}

.tutorial-card .card-body {
    display: flex;
    flex-direction: column;
    height: calc(100% - 160px);
}

.tutorial-card .card-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.tutorial-card .card-text {
    color: #6c757d;
    flex-grow: 1;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.tutorial-card .card-footer {
    background: none;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.75rem 1.25rem;
}

/* Badges */
.badge-nivel {
    font-weight: 500;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
}

.badge-iniciante {
    background-color: #d1e7dd;
    color: #0a3622;
}

.badge-intermediario {
    background-color: #fff3cd;
    color: #664d03;
}

.badge-avancado {
    background-color: #f8d7da;
    color: #58151c;
}

/* Visualização em Lista */
.tutorial-list-item {
    transition: background-color 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
}

.tutorial-list-item:hover {
    background-color: #f8f9fa;
}

.tutorial-list-item .row {
    align-items: center;
}

.tutorial-list-item .tutorial-img {
    height: 120px;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 2rem;
}

/* Modal de Tutorial */
#modalTutorial .modal-content {
    border: none;
    border-radius: 0.5rem;
    overflow: hidden;
}

#modalTutorial .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1.25rem 2rem;
}

#modalTutorial .modal-title {
    font-weight: 600;
    color: #2c3e50;
}

#modalTutorial .modal-body {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

#modalTutorial .modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 2rem;
}

/* Responsividade */
@media (max-width: 767.98px) {
    .tutorial-card .card-img-top {
        height: 120px;
    }
    
    .tutorial-list-item .tutorial-img {
        height: 100px;
    }
    
    #modalTutorial .modal-body {
        padding: 1.25rem;
    }
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tutorial-card, .tutorial-list-item {
    animation: fadeIn 0.3s ease-out forwards;
}
