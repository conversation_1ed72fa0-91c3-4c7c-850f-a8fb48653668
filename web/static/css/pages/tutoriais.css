/* Estilos para a página de Tutoriais */

/* Estilo do card de tutorial */
.tutorial-card {
    transition: transform 0.2s, box-shadow 0.2s;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tutorial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.tutorial-card .card-img-top {
    height: 160px;
    object-fit: cover;
}

.tutorial-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.tutorial-card .card-text {
    flex-grow: 1;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

/* Badges de categoria/dificuldade */
.badge-categoria {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Estilo para o modal de visualização */
#modalTutorial .modal-content {
    border: none;
    border-radius: 0.5rem;
}

#modalTutorial .modal-header {
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
}

#modalTutorial .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Estilo para os passos do tutorial */
.passo-tutorial {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.passo-tutorial:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Estilo para imagens no conteúdo do tutorial */
.tutorial-conteudo img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
    margin: 1rem 0;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Estilo para o indicador de favorito */
.favorito-btn {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.favorito-btn:hover {
    background: #fff;
    transform: scale(1.1);
}

.favorito-btn.active {
    color: #ffc107;
}

/* Estilo para a barra de pesquisa */
.search-bar {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-bar .form-control {
    padding-left: 2.5rem;
    border-radius: 2rem;
}

.search-bar .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Responsividade */
@media (max-width: 767.98px) {
    .tutorial-card {
        margin-bottom: 1.5rem;
    }
    
    #modalTutorial .modal-dialog {
        margin: 0.5rem;
    }
    
    #modalTutorial .modal-body {
        max-height: 60vh;
    }
}
