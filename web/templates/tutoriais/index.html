{{/* Estende o template base_sidemenu */}}
{{ template "base_sidemenu" . }}

{{/* Define o título da página */}}
{{ define "title" }}Tutoriais - Manutenção de Equipamentos{{ end }}

{{/* Define o conteúdo principal */}}
{{ define "content" }}
<div class="container-fluid">
    <div class="page-header">
        <h1><i class="fas fa-graduation-cap me-2"></i>Tutoriais</h1>
        <p class="lead">Aprenda a utilizar e realizar manutenções básicas nos equipamentos da sua filial</p>
    </div>
    
    <div class="row">
        <!-- Filtros -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtros</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="equipamento" class="form-label">Equipamento</label>
                        <select class="form-select" id="equipamento">
                            <option value="" selected>Todos</option>
                            <!-- Opções serão preenchidas via JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="tipo" class="form-label">Tipo</label>
                        <select class="form-select" id="tipo">
                            <option value="" selected>Todos</option>
                            <option value="manutencao">Manutenção</option>
                            <option value="operacao">Operação</option>
                            <option value="seguranca">Segurança</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="dificuldade" class="form-label">Dificuldade</label>
                        <select class="form-select" id="dificuldade">
                            <option value="" selected>Todas</option>
                            <option value="iniciante">Iniciante</option>
                            <option value="intermediario">Intermediário</option>
                            <option value="avancado">Avançado</option>
                        </select>
                    </div>
                    <button class="btn btn-primary w-100" id="aplicarFiltros">
                        <i class="fas fa-check me-2"></i>Aplicar Filtros
                    </button>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informações</h5>
                </div>
                <div class="card-body">
                    <p class="small">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        Utilize os filtros para encontrar tutoriais específicos para suas necessidades.
                    </p>
                    <p class="small">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        Siga todas as instruções de segurança ao realizar manutenções.
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Lista de Tutoriais -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Tutoriais Disponíveis</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" id="btnVisualizacao" data-view="grid">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" id="btnLista" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Mensagem quando não houver tutoriais -->
                    <div id="semTutoriais" class="text-center py-5 d-none">
                        <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                        <h5>Nenhum tutorial encontrado</h5>
                        <p class="text-muted">Tente ajustar os filtros ou verifique novamente mais tarde.</p>
                    </div>
                    
                    <!-- Grid de Tutoriais -->
                    <div id="gridTutoriais" class="row g-4">
                        <!-- Os tutoriais serão carregados aqui via JavaScript -->
                    </div>
                    
                    <!-- Paginação -->
                    <nav class="mt-4" aria-label="Navegação de tutoriais">
                        <ul class="pagination justify-content-center" id="paginacao">
                            <!-- Paginação será gerada via JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para visualização do tutorial -->
<div class="modal fade" id="modalTutorial" tabindex="-1" aria-labelledby="modalTutorialLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTutorialLabel">Carregando...</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body" id="conteudoTutorial">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-3">Carregando tutorial...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Fechar
                </button>
                <button type="button" class="btn btn-primary" id="btnImprimirTutorial">
                    <i class="fas fa-print me-2"></i>Imprimir
                </button>
            </div>
        </div>
    </div>
</div>
{{ end }}

{{/* Define os estilos específicos da página */}}
{{ define "styles" }}
<link href="/static/css/pages/tutoriais/tutoriais.css" rel="stylesheet">
{{ end }}

{{/* Define os scripts específicos da página */}}
{{ define "scripts" }}
<script src="/static/js/pages/tutoriais/tutoriais.js"></script>
{{ end }}
