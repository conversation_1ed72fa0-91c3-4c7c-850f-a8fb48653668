{{ define "layouts/sidebar-proposta.html" }}
<!-- Carrega o CSS e JS da sidebar -->
<link rel="stylesheet" href="/static/css/sidebar.css">
<link rel="stylesheet" href="/static/css/sidebar-profile.css">
<link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&display=swap" rel="stylesheet">
<script src="/static/js/sidebar.js" defer></script>

<div class="tradicio-sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <span class="logo-text">Rede Tradição</span>
        </div>
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <!-- Perfil do Usuário -->
    <div class="sidebar-profile">
        <div class="profile-avatar" data-tooltip="{{ if .User }}{{ .User.Name }}{{ else }}Usuário{{ end }}">
            {{ if and .User .User.Name (gt (len .User.Name) 0) }}{{ slice .User.Name 0 1 }}{{ else }}U{{ end }}
        </div>
        <div class="profile-info">
            <div class="profile-name">{{ if .User }}{{ .User.Name }}{{ else }}Usuário{{ end }}</div>
            <div class="profile-role">{{ if and .User .User.Role }}{{ .User.Role }}{{ else }}Convidado{{ end }}</div>
        </div>
    </div>

    <div class="sidebar-divider"></div>

    <nav class="sidebar-nav">
        <ul class="nav-list">
            <!-- Dashboard -->
            <li class="nav-section">
                <span class="nav-section-title">Principal</span>
            </li>
            <li class="nav-item{{ if eq .ActivePage "dashboard" }} active{{ end }}">
                <a href="/dashboard" class="nav-link" data-tooltip="Dashboard">
                    <i class="fas fa-tachometer-alt nav-icon"></i>
                    <span class="nav-text">Dashboard</span>
                </a>
            </li>

            <!-- Seção de Ordens -->
            <li class="nav-section">
                <span class="nav-section-title">Ordens</span>
            </li>
            {{ if and .User .User.Role (eq .User.Role "tecnico") }}
            <li class="nav-item{{ if eq .ActivePage "ordemtecnico" }} active{{ end }}">
                <a href="/ordemtecnico" class="nav-link" data-tooltip="Ordem Técnica">
                    <i class="fas fa-wrench nav-icon"></i>
                    <span class="nav-text">Ordem Técnica</span>
                </a>
            </li>
            {{ else }}
            <li class="nav-item{{ if eq .ActivePage "calendar" }} active{{ end }}">
                <a href="/calendario" class="nav-link" data-tooltip="Calendário">
                    <i class="fas fa-calendar-alt nav-icon"></i>
                    <span class="nav-text">Calendário</span>
                </a>
            </li>
            <li class="nav-item{{ if eq .ActivePage "orders" }} active{{ end }}">
                <a href="/orders" class="nav-link" data-tooltip="Ordens de Serviço">
                    <i class="fas fa-clipboard-list nav-icon"></i>
                    <span class="nav-text">Ordens de Serviço</span>
                </a>
            </li>
            {{ end }}

            <!-- Seção de Cadastros -->
            <li class="nav-section">
                <span class="nav-section-title">Cadastros</span>
            </li>
            <li class="nav-item{{ if eq .ActivePage "galeria" }} active{{ end }}">
                <a href="/galeria" class="nav-link" data-tooltip="Postos">
                    <i class="fas fa-gas-pump nav-icon"></i>
                    <span class="nav-text">Postos</span>
                </a>
            </li>

            <!-- Seção de Perfil -->
            <li class="nav-section">
                <span class="nav-section-title">Perfil</span>
            </li>
            <li class="nav-item{{ if eq .ActivePage "minha-conta" }} active{{ end }}">
                <a href="/minha-conta" class="nav-link" data-tooltip="Minha Conta">
                    <i class="fas fa-user nav-icon"></i>
                    <span class="nav-text">Minha Conta</span>
                </a>
            </li>
            {{ if and .User .User.Role (eq .User.Role "filial") }}
            <li class="nav-item{{ if eq .ActivePage "minha-filial" }} active{{ end }}">
                <a href="/minha-filial" class="nav-link" data-tooltip="Minha Filial">
                    <i class="fas fa-building nav-icon"></i>
                    <span class="nav-text">Minha Filial</span>
                </a>
            </li>
            {{ end }}
            {{ if and .User .User.Role (eq .User.Role "prestadores") }}
            <li class="nav-item{{ if eq .ActivePage "perfil_empresa" }} active{{ end }}">
                <a href="/prestadoras/perfil_empresa" class="nav-link" data-tooltip="Perfil da Empresa">
                    <i class="fas fa-building nav-icon"></i>
                    <span class="nav-text">Perfil da Empresa</span>
                </a>
            </li>
            {{ end }}

            <!-- Seção Administrativa -->
            {{ if and .User .User.Role (or (eq .User.Role "admin") (eq .User.Role "gerente") (eq .User.Role "financeiro")) }}
            <li class="nav-section">
                <span class="nav-section-title">Administrativo</span>
            </li>
            <li class="nav-item{{ if eq .ActivePage "link-management" }} active{{ end }}">
                <a href="/admin/link-management" class="nav-link" data-tooltip="Gerenciamento de Vínculos">
                    <i class="fas fa-link nav-icon"></i>
                    <span class="nav-text">Gerenciar Vínculos</span>
                </a>
            </li>
            {{ end }}
        </ul>
    </nav>

    <div class="sidebar-footer">
        <a href="/logout" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            <span>Sair</span>
        </a>
    </div>

    <input type="hidden" id="currentUserID" value="{{ if .User }}{{ .User.ID }}{{ end }}">
</div>
{{ end }}

{{ define "sidebar-proposta" }}
{{ template "layouts/sidebar-proposta.html" . }}
{{ end }}
