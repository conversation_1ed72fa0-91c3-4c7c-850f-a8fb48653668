{{ define "base_sidemenu" }}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Sistema de gerenciamento de manutenção para a Rede Tradição Shell">
    <meta name="theme-color" content="#FDB813">
    <title>{{ .title }}</title>

    <!-- PWA Support -->
    <link rel="manifest" href="/static/manifest.json">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-title" content="Shell Manutenção">
    <link rel="apple-touch-icon" href="/static/images/icons/icon-152x152.png">

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/sidemenu.css">

    <!-- CSS específico de cada página -->
    {{ block "head_scripts" . }}{{ end }}

    <style>
        /* Estilos para o sistema de notificações */
        .notifications-container {
            position: relative;
            display: inline-block;
        }

        .notification-toggle {
            background: transparent;
            border: none;
            font-size: 1.2rem;
            color: #f4b31d;
            cursor: pointer;
            position: relative;
        }

        .notification-counter {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #ED1C24;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .notifications-panel {
            position: absolute;
            top: 100%;
            right: 0;
            width: 350px;
            max-height: 0;
            overflow: hidden;
            background: rgba(40, 30, 20, 0.9);
            border: 2px solid #f4b31d;
            border-radius: 8px;
            z-index: 1000;
            transition: max-height 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }

        .notifications-panel.open {
            max-height: 500px;
            overflow-y: auto;
        }

        .notifications-header {
            background: rgba(60, 40, 10, 0.9);
            color: #f4b31d;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #654321;
        }

        .notifications-list {
            padding: 0;
        }

        .notification-item {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(101, 67, 33, 0.3);
            cursor: default;
            transition: background 0.2s ease;
            background: rgba(50, 40, 30, 0.7);
        }

        .notification-item:hover {
            background: rgba(70, 50, 30, 0.7);
        }

        .notification-item.read {
            opacity: 0.7;
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
        }

        .notification-header h4 {
            color: #f4b31d;
            font-size: 1rem;
            margin: 0;
        }

        .notification-header small {
            color: #ccc;
            font-size: 0.7rem;
        }

        .notification-body p {
            color: white;
            margin: 0;
            font-size: 0.9rem;
        }

        .notification-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 8px;
        }

        .empty-notifications {
            padding: 20px;
            text-align: center;
            color: #ccc;
            font-style: italic;
        }
    </style>
    <style>
        .barra-icones {
            background-color: #f8f9fa;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }

        .barra-icones img, .barra-icones i {
            height: 24px;
            margin-right: 15px;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="barra-icones">
        <img src="/static/images/trator.png" alt="Trator">
        <img src="/static/images/mala.png" alt="Mala">
        <img src="/static/images/bomba.png" alt="Bomba">
        <img src="/static/images/barco.png" alt="Barco">
        <img src="/static/images/caminhao.png" alt="Caminhão">
    </div>

    <div class="wrapper">
        <!-- Sidebar (Menu Lateral) -->
        <nav id="sidebar" class="active">
            <div class="sidebar-header">
                <a href="/" class="logo">
                    <span class="logo-text">PAINEL CONTROLE</span>
                </a>
                <button class="toggle-btn" id="sidebar-toggle-icon">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>

            <div class="sidebar-section">
                <h6 class="sidebar-section-title">Principal</h6>
                <ul class="nav-menu">
                    <li class="nav-item {{ if eq .page "dashboard" }}active{{ end }}">
                        <a href="/dashboard" class="nav-link">
                            <div class="nav-icon">
                                <i class="fas fa-tachometer-alt"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text">Dashboard</span>
                            <span class="nav-tooltip">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/calendario" class="nav-link {{ if eq .page "calendario" }}active{{ end }}" style="{{ if eq .page "calendario" }}background-color: #FDB813; border-radius: 6px; box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1); transform: scale(1.05); transition: all 0.3s ease;{{ end }}">
                            <div class="nav-icon">
                                <i class="fas fa-calendar-alt" style="{{ if eq .page "calendario" }}color: #D00; font-size: 1.2em;{{ end }}"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text" style="{{ if eq .page "calendario" }}font-weight: bold; color: #D00; text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);{{ end }}">CALENDÁRIO AVANÇADO</span>
                            <span class="nav-tooltip">Calendário Shell</span>
                        </a>
                    </li>

                    <li class="nav-item {{ if eq .page "calendar_layout" }}active{{ end }}">
                        <a href="/calendar-layout" class="nav-link">
                            <div class="nav-icon">
                                <i class="fas fa-calendar-check"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text">CALENDÁRIO COM APROVAÇÕES</span>
                            <span class="nav-tooltip">Calendário com Aprovações</span>
                        </a>
                    </li>
                    <li class="nav-item {{ if eq .page "create-order" }}active{{ end }}">
                        <a href="/create-order" class="nav-link">
                            <div class="nav-icon">
                                <i class="fas fa-plus-circle"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text">Nova Ordem</span>
                            <span class="nav-tooltip">Nova Ordem</span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="sidebar-section">
                <h6 class="sidebar-section-title">Gerenciamento</h6>
                <ul class="nav-menu">

                    <li class="nav-item {{ if eq .page "orders" }}active{{ end }}">
                        <a href="/orders" class="nav-link">
                            <div class="nav-icon">
                                <i class="fas fa-clipboard-list"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text">Ordens</span>
                            <span class="nav-tooltip">Ordens</span>
                        </a>
                    </li>
                    <li class="nav-item {{ if eq .page "financeiro" }}active{{ end }}">
                        <a href="/financeiro" class="nav-link">
                            <div class="nav-icon">
                                <i class="fas fa-dollar-sign"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text">Financeiro</span>
                            <span class="nav-tooltip">Financeiro</span>
                        </a>
                    </li>
                </ul>
            </div>



            <div class="sidebar-section">
                <h6 class="sidebar-section-title">Sistema</h6>
                <ul class="nav-menu">

                    <li class="nav-item {{ if eq .page "minha-conta" }}active{{ end }}">
                        <a href="/minha-conta" class="nav-link">
                            <div class="nav-icon">
                                <i class="fas fa-user"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text">Minha Conta</span>
                            <span class="nav-tooltip">Minha Conta</span>
                        </a>
                    </li>
                    <li class="nav-item {{ if eq .page "tutoriais" }}active{{ end }}">
                        <a href="/tutoriais" class="nav-link">
                            <div class="nav-icon">
                                <i class="fas fa-graduation-cap"></i>
                                <span class="fuel-drop"></span>
                            </div>
                            <span class="nav-text">Tutoriais</span>
                            <span class="nav-tooltip">Tutoriais de Manutenção</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Conteúdo da Página -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-outline-danger">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h2 class="ms-3">Sistema Tradição</h2>

                    <!-- Área de usuário e notificações -->
                    <div class="ms-auto d-flex align-items-center">
                        <!-- Sistema de notificações -->
                        <div class="notifications-container me-4">
                            <button class="notification-toggle" aria-label="Notificações">
                                <i class="fas fa-bell"></i>
                                <span class="notification-counter" style="display: none;">0</span>
                            </button>

                            <!-- Painel de notificações -->
                            <div class="notifications-panel">
                                <div class="notifications-header">
                                    <h5 class="m-0">Notificações</h5>
                                    <button class="btn btn-sm js-push-btn" style="color: #f4b31d;">Ativar Notificações</button>
                                </div>
                                <div class="notifications-list">
                                    <!-- Removido o texto de carregamento que estava causando problemas -->
                                </div>
                            </div>
                        </div>

                        <!-- Informações do usuário -->
                        <div class="user-info d-flex align-items-center">
                            <span id="user-role" data-role="{{ if .User }}{{ .User.Role }}{{ else }}usuario{{ end }}" style="display: none;"></span>
                            <span class="me-2 text-dark">{{ if .User }}{{ .User.Name }}{{ else }}Usuário{{ end }}</span>
                            <a href="/logout" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-sign-out-alt"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </nav>

            <div class="container-fluid my-4">
                {{ template "content" . }}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/notifications.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle do sidebar
            document.getElementById('sidebarCollapse').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('active');
            });

            // Inicializa todos os tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Botão de teste de notificação (apenas para desenvolvimento)
            if (document.querySelector('.js-test-notification-btn') === null) {
                const testBtn = document.createElement('button');
                testBtn.className = 'js-test-notification-btn btn btn-sm btn-primary mt-2';
                testBtn.textContent = 'Testar Notificação';
                testBtn.style.display = 'none'; // Oculto por padrão, apenas para desenvolvimento

                const notificationsHeader = document.querySelector('.notifications-header');
                if (notificationsHeader) {
                    notificationsHeader.appendChild(testBtn);
                }
            }
        });
    </script>

    <!-- Scripts específicos de cada página -->
    {{ block "scripts" . }}{{ end }}

    <!-- PWA Registration Script -->
    <script src="/static/js/pwa-register.js"></script>
</body>
</html>
{{ end }}