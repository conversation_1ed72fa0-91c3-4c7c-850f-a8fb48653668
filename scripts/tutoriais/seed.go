package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"tradicao/internal/models"
)

func main() {
	// Conectar ao banco de dados
	db, err := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Criar as tabelas se não existirem
	err = db.AutoMigrate(
		&models.Tutorial{},
		&models.TutorialFavorito{},
		&models.HistoricoVisualizacao{},
	)
	if err != nil {
		log.Fatalf("Erro ao migrar modelos: %v", err)
	}

	// Criar usuário de teste se não existir
	var user models.User
	if err := db.FirstOrCreate(&user, models.User{
		ID:        1,
		Name:      "Usuário Teste",
		Email:     "<EMAIL>",
		Role:      "admin",
		Active:    true,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}).Error; err != nil {
		log.Printf("Aviso: Não foi possível criar usuário de teste: %v", err)
	}

	// Criar tutoriais de exemplo
	tutoriais := []models.Tutorial{
		{
			Titulo:        "Manutenção Preventiva de Bombas",
			Descricao:     "Passo a passo para manutenção preventiva em bombas de combustível",
			Conteudo:      "Conteúdo detalhado sobre manutenção preventiva...",
			Tipo:          models.TipoManutencao,
			Nivel:         models.NivelIntermediario,
			Categoria:     "Bombas",
			DuracaoMinutos: 30,
			ImagemURL:     "/static/images/tutoriais/bomba.jpg",
			EquipamentoID: nil,
			FilialID:      nil,
			AutorID:       1,
			Tags:          "manutencao, bomba, combustivel",
		},
		{
			Titulo:        "Operação de Tanques de Armazenamento",
			Descricao:     "Como operar corretamente os tanques de armazenamento de combustível",
			Conteudo:      "Conteúdo detalhado sobre operação de tanques...",
			Tipo:          models.TipoOperacao,
			Nivel:         models.NivelIniciante,
			Categoria:     "Tanques",
			DuracaoMinutos: 20,
			ImagemURL:     "/static/images/tutoriais/tanque.jpg",
			EquipamentoID: nil,
			FilialID:      nil,
			AutorID:       1,
			Tags:          "operacao, tanque, armazenamento",
		},
		{
			Titulo:        "Procedimentos de Segurança em Postos",
			Descricao:     "Normas e procedimentos de segurança essenciais para postos de combustível",
			Conteudo:      "Conteúdo detalhado sobre procedimentos de segurança...",
			Tipo:          models.TipoSeguranca,
			Nivel:         models.NivelAvancado,
			Categoria:     "Segurança",
			DuracaoMinutos: 45,
			VideoURL:      "https://www.youtube.com/watch?v=exemplo",
			EquipamentoID: nil,
			FilialID:      nil,
			AutorID:       1,
			Tags:          "seguranca, normas, emergencia",
		},
	}

	// Inserir tutoriais
	for i := range tutoriais {
		tutoriais[i].CreatedAt = time.Now()
		tutoriais[i].UpdatedAt = time.Now()
		if err := db.Create(&tutoriais[i]).Error; err != nil {
			log.Printf("Erro ao criar tutorial %d: %v", i+1, err)
		}
	}

	// Criar diretório de imagens se não existir
	if err := os.MkdirAll("web/static/images/tutoriais", 0755); err != nil {
		log.Printf("Aviso: Não foi possível criar diretório de imagens: %v", err)
	}

	fmt.Println("Seed de tutoriais concluído com sucesso!")
}
