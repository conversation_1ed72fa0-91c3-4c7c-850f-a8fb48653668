#!/bin/bash

# Memory Manager for Cascade
# Gerencia as memórias do agente, workspace, projeto e Cascade

MEMORY_DIR=".cascade/memories"
CONFIG_FILES=(
  "agent/agent_config.json"
  "workspace/workspace_config.json"
  "project/project_rules.json"
  "cascade/cascade_config.json"
)

# Função para verificar se o diretório de memórias existe
check_memory_dir() {
  if [ ! -d "$MEMORY_DIR" ]; then
    echo "Diretório de memórias não encontrado. Criando..."
    mkdir -p "$MEMORY_DIR/agent" "$MEMORY_DIR/workspace" "$MEMORY_DIR/project" "$MEMORY_DIR/cascade"
  fi
}

# Função para listar todas as memórias
list_memories() {
  echo "Memórias disponíveis:"
  for config in "${CONFIG_FILES[@]}"; do
    if [ -f "$MEMORY_DIR/$config" ]; then
      echo "- $config"
    fi
  done
}

# Função para visualizar uma memória específica
view_memory() {
  local memory=$1
  local found=false
  
  for config in "${CONFIG_FILES[@]}"; do
    if [ "$memory" = "$(basename "$config" .json)" ] || [ "$memory" = "$config" ]; then
      jq . "$MEMORY_DIR/$config"
      found=true
      break
    fi
  done
  
  if [ "$found" = false ]; then
    echo "Memória não encontrada: $memory"
    return 1
  fi
}

# Função para atualizar uma memória
update_memory() {
  local memory=$1
  local key=$2
  local value=$3
  local found=false
  
  for config in "${CONFIG_FILES[@]}"; do
    if [ "$memory" = "$(basename "$config" .json)" ] || [[ "$config" == *"$memory"* ]]; then
      jq ".$key = $value" "$MEMORY_DIR/$config" > "$MEMORY_DIR/$config.tmp" && mv "$MEMORY_DIR/$config.tmp" "$MEMORY_DIR/$config"
      echo "Memória atualizada com sucesso: $memory"
      found=true
      break
    fi
  done
  
  if [ "$found" = false ]; then
    echo "Memória não encontrada: $memory"
    return 1
  fi
}

# Função principal
main() {
  check_memory_dir
  
  case "$1" in
    list)
      list_memories
      ;;
    view)
      if [ -z "$2" ]; then
        echo "Uso: $0 view <nome_da_memoria>"
        exit 1
      fi
      view_memory "$2"
      ;;
    update)
      if [ -z "$3" ] || [ -z "$4" ]; then
        echo "Uso: $0 update <nome_da_memoria> <chave> <valor>"
        exit 1
      fi
      update_memory "$2" "$3" "$4"
      ;;
    *)
      echo "Uso: $0 <comando> [argumentos]"
      echo ""
      echo "Comandos disponíveis:"
      echo "  list                  Lista todas as memórias disponíveis"
      echo "  view <memoria>        Visualiza o conteúdo de uma memória"
      echo "  update <memoria> <chave> <valor>  Atualiza um valor na memória"
      ;;
  esac
}

main "$@"
