#!/bin/bash

# Atualiza o timestamp de last_modified em todos os arquivos de configuração

CONFIG_FILES=(
  "memories/agent/agent_config.json"
  "memories/workspace/workspace_config.json"
  "memories/project/project_rules.json"
  "memories/cascade/cascade_config.json"
)

TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S-03:00")

for config in "${CONFIG_FILES[@]}"; do
  if [ -f "$config" ]; then
    # Usando uma abordagem mais robusta com jq
    jq --arg timestamp "$TIMESTAMP" '.last_updated = $timestamp' "$config" > "${config}.tmp" && mv "${config}.tmp" "$config"
    echo "Atualizado: $config"
  else
    echo "Aviso: Arquivo não encontrado: $config"
  fi
done

echo "Todos os timestamps foram atualizados para: $TIMESTAMP"
