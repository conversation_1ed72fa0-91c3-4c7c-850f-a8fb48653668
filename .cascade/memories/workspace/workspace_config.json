{"workspace_id": "projeto_linux_001", "root_path": "/home/<USER>/projeto_linux", "project_type": "go", "detected_frameworks": ["gin", "gorm"], "important_paths": {"source_code": "/internal", "api_endpoints": "/api", "configs": "/configs", "migrations": "/migrations", "templates": "/web/templates"}, "build_tools": {"go_version": "1.21", "build_command": "go build -o bin/main ./cmd/...", "test_command": "go test ./..."}, "dependencies": {"go": ">=1.20", "github.com/gin-gonic/gin": "^1.9.0", "gorm.io/gorm": "^1.25.0"}, "created_at": "2025-06-08T08:42:50-03:00", "last_updated": "2025-06-08T08:42:50-03:00"}