{"agent_id": "cascade_agent_001", "version": "1.0.0", "preferences": {"language": "pt-BR", "timezone": "America/Sao_Paulo", "verbosity": "detailed", "max_token_limit": 4000, "response_style": "concise", "memory_management": {"enabled": true, "auto_save": true, "max_context_size": 10000, "commands": {"list_memories": "./.cascade/memory_manager.sh list", "view_memory": "./.cascade/memory_manager.sh view {memory_name}", "update_memory": "./.cascade/memory_manager.sh update {memory_name} {key} {value}", "examples": ["Listar todas as memórias: ./.cascade/memory_manager.sh list", "Visualizar configuração do agente: ./.cascade/memory_manager.sh view agent_config", "Atualizar idioma: ./.cascade/memory_manager.sh update agent_config preferences.language \"pt-BR\""]}}}, "capabilities": ["code_analysis", "file_operations", "command_execution", "web_search", "memory_management", "context_tracking"], "safety_controls": {"confirm_destructive_actions": true, "max_file_size_mb": 10, "allowed_domains": ["github.com", "go.dev", "pkg.go.dev"], "blocked_commands": ["rm -rf", "dd", "mkfs", "fdisk"]}, "created_at": "2025-06-08T08:42:50-03:00", "last_updated": "2025-06-08T08:42:50-03:00"}