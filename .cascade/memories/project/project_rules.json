{"project_name": "Projeto Linux", "coding_standards": {"language": "pt-BR", "code_style": "standard", "indentation": {"type": "spaces", "size": 2}, "naming_conventions": {"variables": "camelCase", "constants": "UPPER_SNAKE_CASE", "functions": "PascalCase", "private_members": "_camelCase"}, "file_organization": {"max_lines_per_file": 1000, "max_functions_per_file": 15, "max_parameters_per_function": 5}}, "git_workflow": {"branch_naming": "feature/description-or-issue-number", "commit_message": {"format": "type(scope): description\n\nDetailed description", "types": ["feat", "fix", "docs", "style", "refactor", "test", "chore"], "max_length": 72}, "pull_request": {"template": "## Descrição\n\n## Tipo de Mudança\n- [ ] Nova funcionalidade\n- [ ] Correção de bug\n- [ ] Melhoria de desempenho\n\n## Checklist\n- [ ] Código revisado\n- [ ] Testes adicionados/atualizados\n- [ ] Documentação atualizada"}}, "testing": {"coverage_goal": 80, "test_pattern": "*_test.go", "benchmark_pattern": "*_benchmark.go"}, "documentation": {"api_docs": "swagger", "inline_comments": true, "docstrings_required": true}, "security": {"secrets_management": "environment_variables", "vulnerability_scanning": true, "dependency_checking": true}, "ci_cd": {"build_on_push": true, "run_tests": true, "deploy_on_tag": true}, "created_at": "2025-06-08T08:42:50-03:00", "last_updated": "2025-06-08T08:42:50-03:00"}