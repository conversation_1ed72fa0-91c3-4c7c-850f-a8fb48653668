# Sistema de Memória do Cascade

Este diretório contém as configurações e memórias utilizadas pelo Cascade para manter o contexto e as preferências do projeto.

## Estrutura de Diretórios

- `agent/`: Configurações específicas do agente de IA
- `workspace/`: Configurações do ambiente de trabalho
- `project/`: Regras e padrões do projeto
- `cascade/`: Configurações do sistema Cascade

## Arquivos de Configuração

1. **Agent Config** (`agent/agent_config.json`)
   - Preferências e capacidades do agente
   - Configurações de segurança
   - Controles de segurança

2. **Workspace Config** (`workspace/workspace_config.json`)
   - Caminhos importantes
   - Ferramentas de build
   - Dependências

3. **Project Rules** (`project/project_rules.json`)
   - Padrões de código
   - Fluxo de trabalho Git
   - Testes e documentação

4. **Cascade Config** (`cascade/cascade_config.json`)
   - Gerenciamento de memória
   - Configurações de desempenho
   - Integrações

## Gerenciamento de Memória

Use o script `memory_manager.sh` para gerenciar as memórias:

```bash
# Listar todas as memórias
./.cascade/memory_manager.sh list

# Visualizar uma memória específica
./.cascade/memory_manager.sh view agent_config

# Atualizar um valor em uma memória
./.cascade/memory_manager.sh update agent_config preferences.language "pt-BR"
```

## Boas Práticas

1. **Backup**: Faça backup regular do diretório `.cascade`
2. **Versionamento**: Adicione os arquivos de configuração ao controle de versão
3. **Segurança**: Não armazene informações sensíveis em arquivos de configuração
4. **Atualização**: Revise e atualize as configurações regularmente

## Licença

Este projeto está sob a licença [MIT](LICENSE).
