# Referência Rápida - Gerenciamento de Memória

## Comandos Essenciais

### Listar <PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>
```bash
./.cascade/memory_manager.sh list
```

### Visualizar uma Memória Específica
```bash
# Sintaxe
./.cascade/memory_manager.sh view <nome_da_memória>

# Exemplo: Visualizar configuração do agente
./.cascade/memory_manager.sh view agent_config
```

### Atualizar um Valor na Memória
```bash
# Sintaxe
./.cascade/memory_manager.sh update <memória> <chave> "<valor>"

# Exemplo: Atualizar idioma
./.cascade/memory_manager.sh update agent_config preferences.language "\"pt-BR\""

# Exemplo: Atualizar timezone
./.cascade/memory_manager.sh update agent_config preferences.timezone "\"America/Sao_Paulo\""
```

## Memórias Disponíveis

1. **agent_config** - Configurações do agente Cascade
   - Caminho: `.cascade/memories/agent/agent_config.json`
   - Contém: Preferências, capacidades e controles de segurança

2. **workspace_config** - Configurações do workspace
   - Caminho: `.cascade/memories/workspace/workspace_config.json`
   - Contém: Estrutura do projeto, ferramentas e dependências

3. **project_rules** - Regras do projeto
   - Caminho: `.cascade/memories/project/project_rules.json`
   - Contém: Padrões de código, fluxo Git e diretrizes

4. **cascade_config** - Configurações do sistema Cascade
   - Caminho: `.cascade/memories/cascade/cascade_config.json`
   - Contém: Configurações de desempenho e gerenciamento

## Dicas Rápidas

- Use `jq` para filtrar saídas JSON:
  ```bash
  ./.cascade/memory_manager.sh view agent_config | jq '.preferences.language'
  ```

- Atualize a data de modificação ao fazer alterações:
  ```bash
  ./update_last_modified.sh
  ```

- Consulte o README completo para mais detalhes:
  ```bash
  cat .cascade/README.md
  ```
