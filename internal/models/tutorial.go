package models

import (
	"time"
)

// NívelDificuldade define os níveis de dificuldade dos tutoriais
type NivelDificuldade string

const (
	NivelIniciante     NivelDificuldade = "iniciante"
	NivelIntermediario NivelDificuldade = "intermediario"
	NivelAvancado      NivelDificuldade = "avancado"
)

// TipoTutorial define os tipos de tutoriais disponíveis
type TipoTutorial string

const (
	TipoManutencao TipoTutorial = "manutencao"
	TipoOperacao   TipoTutorial = "operacao"
	TipoSeguranca  TipoTutorial = "seguranca"
)

// Tutorial representa um tutorial de manutenção ou operação de equipamentos
type Tutorial struct {
	ID            uint            `json:"id" gorm:"primaryKey"`
	Titulo        string          `json:"titulo" gorm:"size:200;not null"`
	Descricao     string          `json:"descricao" gorm:"type:text"`
	Conteudo      string          `json:"conteudo" gorm:"type:text;not null"`
	Tipo          TipoTutorial    `json:"tipo" gorm:"type:varchar(20);not null;default:'manutencao'"`
	Nivel         NivelDificuldade `json:"nivel" gorm:"type:varchar(20);not null;default:'iniciante'"`
	Categoria     string          `json:"categoria" gorm:"size:100"`
	DuracaoMinutos int             `json:"duracao_minutos" gorm:"default:10"`
	ImagemURL     string          `json:"imagem_url" gorm:"size:255"`
	VideoURL      string          `json:"video_url" gorm:"size:255"`
	EquipamentoID *uint           `json:"equipamento_id"`
	Equipamento   *Equipamento    `json:"equipamento,omitempty" gorm:"foreignKey:EquipamentoID"`
	FilialID      *uint           `json:"filial_id"`
	Filial        *Filial         `json:"filial,omitempty" gorm:"foreignKey:FilialID"`
	AutorID       uint            `json:"autor_id"`
	Autor         *User           `json:"autor,omitempty" gorm:"foreignKey:AutorID"`
	Visualizacoes int             `json:"visualizacoes" gorm:"default:0"`
	Avaliacao     float64         `json:"avaliacao" gorm:"default:0"`
	Ativo         bool            `json:"ativo" gorm:"default:true"`
	Tags          string          `json:"tags" gorm:"type:text"` // Tags separadas por vírgula
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
	DeletedAt    *time.Time      `json:"-" gorm:"index"`
}

// TutorialFavorito representa os tutoriais favoritados pelos usuários
type TutorialFavorito struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UsuarioID  uint      `json:"usuario_id" gorm:"index"`
	TutorialID uint      `json:"tutorial_id" gorm:"index"`
	CreatedAt  time.Time `json:"created_at"`

	Usuario    *User     `json:"usuario,omitempty" gorm:"foreignKey:UsuarioID"`
	Tutorial   *Tutorial `json:"tutorial,omitempty" gorm:"foreignKey:TutorialID"`
}

// HistoricoVisualizacao registra o histórico de visualização de tutoriais
type HistoricoVisualizacao struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UsuarioID  uint      `json:"usuario_id" gorm:"index"`
	TutorialID uint      `json:"tutorial_id" gorm:"index"`
	VisualizadoEm time.Time `json:"visualizado_em"`
	TempoAssistido int     `json:"tempo_assistido"` // em segundos

	Usuario    *User     `json:"usuario,omitempty" gorm:"foreignKey:UsuarioID"`
	Tutorial   *Tutorial `json:"tutorial,omitempty" gorm:"foreignKey:TutorialID"`
}

func (Tutorial) TableName() string {
	return "tutoriais"
}

func (TutorialFavorito) TableName() string {
	return "tutoriais_favoritos"
}

func (HistoricoVisualizacao) TableName() string {
	return "tutoriais_historico_visualizacao"
}
