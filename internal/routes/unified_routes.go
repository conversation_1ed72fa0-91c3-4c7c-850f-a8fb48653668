package routes

import (
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupUnifiedRoutes configura as rotas unificadas para o sistema
func SetupUnifiedRoutes(router *gin.Engine, unifiedHandler *handlers.UnifiedFilialHandler, authService services.AuthService) {
	// Grupo de rotas para filiais unificadas
	filiais := router.Group("/api/unified/filiais")
	{
		// Rotas de leitura (acesso público)
		filiais.GET("", unifiedHandler.GetAllFiliais)
		filiais.GET("/:id", unifiedHandler.GetFilial) // Alterado de GetFilialByID para GetFilial

		// Rotas de escrita (requerem autenticação e permissão)
		authorized := filiais.Group("")
		authorized.Use(middleware.AuthMiddleware(authService))
		authorized.Use(middleware.RoleMiddleware("admin", "gerente"))
		{
			authorized.POST("", unifiedHandler.CreateFilial)
			authorized.PUT("/:id", unifiedHandler.UpdateFilial)
			authorized.DELETE("/:id", unifiedHandler.DeleteFilial)
		}

		// Rotas adicionais
		// Nota: Os métodos GetFiliaisByRegion, GetActiveFiliais e GetFilialMetrics
		// não estão implementados no UnifiedFilialHandler. Eles podem ser implementados
		// posteriormente se necessário.
	}
}
