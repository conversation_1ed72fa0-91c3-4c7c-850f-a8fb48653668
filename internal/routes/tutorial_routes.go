package routes

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"tradicao/internal/handlers"
	"tradicao/internal/interfaces"
	"tradicao/internal/middleware"
	"tradicao/internal/repository"
	"tradicao/internal/services"
)

// SetupTutorialRoutes configura as rotas relacionadas a tutoriais
func SetupTutorialRoutes(router *gin.Engine, db *gorm.DB, authService interfaces.AuthServiceInterface) {
	// Inicializa o repositório
	tutorialRepo := repository.NewTutorialRepository(db)
	
	// Inicializa o serviço
	tutorialService := services.NewTutorialService(tutorialRepo)
	
	// Inicializa o handler
	tutorialHandler := handlers.NewTutorialHandler(tutorialService)
	
	// Middleware de autenticação
	authMiddleware := middleware.AuthMiddleware(authService)

	// Grupo de rotas da API para tutoriais
	api := router.Group("/api")
	{
		// Rotas públicas
		public := api.Group("/tutoriais")
		{
			public.GET("", tutorialHandler.ListTutoriais)
			public.GET("/:id", tutorialHandler.GetTutorial)
			public.GET("/estatisticas", tutorialHandler.ObterEstatisticas)
		}

		// Rotas autenticadas
		auth := api.Group("/tutoriais")
		auth.Use(authMiddleware)
		{
			auth.POST("", tutorialHandler.CreateTutorial)
			auth.PUT("/:id", tutorialHandler.UpdateTutorial)
			auth.DELETE("/:id", tutorialHandler.DeleteTutorial)
			auth.POST("/:id/favorito", tutorialHandler.AdicionarFavorito)
			auth.DELETE("/:id/favorito", tutorialHandler.RemoverFavorito)
			auth.GET("/favoritos", tutorialHandler.ListarFavoritos)
		}
	}
	
	// Rota para a página web de tutoriais
	router.GET("/tutoriais", func(c *gin.Context) {
		c.HTML(200, "tutoriais/index.html", gin.H{
			"title": "Tutoriais",
		})
	})
}
