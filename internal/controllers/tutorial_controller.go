package controllers

import (
	"context"
	"net/http"
	"strconv"

	"tradicao/internal/agents/tutorial"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"github.com/gin-gonic/gin"
)

// TutorialController expõe endpoints para CRUD de tutoriais
type TutorialController struct {
	tutorialAgent *tutorial.TutorialAgent
}

func NewTutorialController(agent *tutorial.TutorialAgent) *TutorialController {
	return &TutorialController{tutorialAgent: agent}
}

// POST /api/tutorials
func (c *TutorialController) Create(ctx *gin.Context) {
	var tutorial models.Tutorial
	if err := ctx.ShouldBindJSON(&tutorial); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}
	if err := c.tutorialAgent.CreateTutorial(context.Background(), &tutorial); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.<PERSON>rror()})
		return
	}
	ctx.JSON(http.StatusCreated, tutorial)
}

// GET /api/tutorials/:id
func (c *TutorialController) GetByID(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}
	tutorial, err := c.tutorialAgent.GetTutorialByID(context.Background(), uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Tutorial não encontrado"})
		return
	}
	ctx.JSON(http.StatusOK, tutorial)
}

// PUT /api/tutorials/:id
func (c *TutorialController) Update(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}
	var tutorial models.Tutorial
	if err := ctx.ShouldBindJSON(&tutorial); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}
	tutorial.ID = uint(id)
	if err := c.tutorialAgent.UpdateTutorial(context.Background(), &tutorial); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, tutorial)
}

// DELETE /api/tutorials/:id
func (c *TutorialController) Delete(ctx *gin.Context) {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}
	if err := c.tutorialAgent.DeleteTutorial(context.Background(), uint(id)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusNoContent, nil)
}

// GET /api/tutorials
func (c *TutorialController) List(ctx *gin.Context) {
	offset, _ := strconv.Atoi(ctx.DefaultQuery("offset", "0"))
	limit, _ := strconv.Atoi(ctx.DefaultQuery("limit", "20"))
	
	// Cria um filtro vazio por padrão
	filters := repository.TutorialFilters{
		// Adicione aqui os parâmetros de filtro da query se necessário
	}
	
	tutorials, total, err := c.tutorialAgent.ListTutorials(context.Background(), filters, offset, limit)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"total": total, "items": tutorials})
}
