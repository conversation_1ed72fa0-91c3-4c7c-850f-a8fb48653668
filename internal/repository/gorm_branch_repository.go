package repository

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"time"
	"tradicao/internal/models"

	"gorm.io/gorm"
)

// GormBranchRepository implementa a interface IBranchRepository usando GORM
type GormBranchRepository struct {
	db *gorm.DB
}

// NewGormBranchRepository cria uma nova instância do repositório de filiais
func NewGormBranchRepository(db *gorm.DB) *GormBranchRepository {
	return &GormBranchRepository{
		db: db,
	}
}

// GetAllBranches retorna todas as filiais com paginação e filtros
func (r *GormBranchRepository) GetAllBranches(page, limit int, search string, isActive *bool) ([]models.Branch, int64, error) {
	var branches []models.Branch
	var total int64

	query := r.db.Model(&models.Branch{})

	if search != "" {
		query = query.Where("name LIKE ? OR city LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Find(&branches).Error; err != nil {
		return nil, 0, err
	}

	return branches, total, nil
}

// GetBranchByID retorna uma filial pelo ID (mantido para compatibilidade)
func (r *GormBranchRepository) GetBranchByID(id uint) (*models.Branch, error) {
	return r.FindByID(context.Background(), id)
}

// FindByID retorna uma filial pelo ID
func (r *GormBranchRepository) FindByID(ctx context.Context, id uint) (*models.Branch, error) {
	var branch models.Branch
	result := r.db.First(&branch, id)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // Retorna nil sem erro quando não encontra
		}
		return nil, result.Error
	}

	return &branch, nil
}

// GetBranchesByUserID retorna filiais associadas a um usuário
func (r *GormBranchRepository) GetBranchesByUserID(ctx context.Context, userID uint) ([]models.Branch, error) {
	var branches []models.Branch

	// Consulta filiais onde o usuário é o gerente ou está associado
	result := r.db.Joins("LEFT JOIN user_branch_assignments ON user_branch_assignments.branch_id = branches.id").
		Where("branches.manager_id = ? OR user_branch_assignments.user_id = ?", userID, userID).
		Find(&branches)

	return branches, result.Error
}

// GetBranchesByTechnicianID retorna filiais onde um técnico está associado a ordens
func (r *GormBranchRepository) GetBranchesByTechnicianID(technicianID uint) ([]models.Branch, error) {
	var branches []models.Branch

	// Consulta filiais das ordens atribuídas ao técnico
	result := r.db.Distinct("branches.*").
		Joins("JOIN maintenance_orders ON maintenance_orders.branch_id = branches.id").
		Where("maintenance_orders.assigned_to = ?", technicianID).
		Find(&branches)

	return branches, result.Error
}

// List retorna todas as filiais
func (r *GormBranchRepository) List(ctx context.Context) ([]models.Branch, error) {
	var branches []models.Branch
	result := r.db.Find(&branches)
	return branches, result.Error
}

// Create cria uma nova filial
func (r *GormBranchRepository) Create(ctx context.Context, branch *models.Branch) error {
	return r.db.Create(branch).Error
}

// Update atualiza uma filial existente
func (r *GormBranchRepository) Update(ctx context.Context, branch *models.Branch) error {
	return r.db.Save(branch).Error
}

// Delete remove uma filial
func (r *GormBranchRepository) Delete(ctx context.Context, id uint) error {
	return r.db.Delete(&models.Branch{}, id).Error
}

// BranchExists verifica se uma filial existe
func (r *GormBranchRepository) BranchExists(id uint) (bool, error) {
	var count int64
	result := r.db.Model(&models.Branch{}).Where("id = ?", id).Count(&count)
	return count > 0, result.Error
}

// LinkProvider vincula um prestador a uma filial
func (r *GormBranchRepository) LinkProvider(branchID, providerID uint) error {
	link := struct {
		BranchID   uint `gorm:"primaryKey"`
		ProviderID uint `gorm:"primaryKey"`
	}{
		BranchID:   branchID,
		ProviderID: providerID,
	}
	return r.db.Table("branch_provider_links").Create(&link).Error
}

// UnlinkProvider remove o vínculo entre um prestador e uma filial
func (r *GormBranchRepository) UnlinkProvider(branchID, providerID uint) error {
	return r.db.Table("branch_provider_links").Where("branch_id = ? AND provider_id = ?", branchID, providerID).Delete("").Error
}

// GetLinkedProviders retorna os IDs dos prestadores vinculados a uma filial
func (r *GormBranchRepository) GetLinkedProviders(branchID uint) ([]uint, error) {
	var providerIDs []uint
	result := r.db.Table("branch_provider_links").Where("branch_id = ?", branchID).Pluck("provider_id", &providerIDs)
	return providerIDs, result.Error
}

// GenerateAuthToken gera um token de autenticação para uma filial
func (r *GormBranchRepository) GenerateAuthToken(branchID uint) (string, error) {
	// Gerar 32 bytes de dados aleatórios
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", err
	}

	// Converter para string hexadecimal
	tokenString := hex.EncodeToString(tokenBytes)

	// Criar o token com validade de 24 horas
	token := struct {
		BranchID  uint      `gorm:"column:branch_id"`
		Token     string    `gorm:"column:token"`
		ExpiresAt time.Time `gorm:"column:expires_at"`
		IsUsed    bool      `gorm:"column:is_used"`
		CreatedAt time.Time `gorm:"column:created_at"`
	}{
		BranchID:  branchID,
		Token:     tokenString,
		ExpiresAt: time.Now().Add(24 * time.Hour),
		IsUsed:    false,
		CreatedAt: time.Now(),
	}

	if err := r.db.Table("branch_auth_tokens").Create(&token).Error; err != nil {
		return "", err
	}

	return tokenString, nil
}

// ValidateAuthToken valida um token de autenticação
func (r *GormBranchRepository) ValidateAuthToken(userID uint, token string) (bool, error) {
	var authToken struct {
		BranchID  uint      `gorm:"column:branch_id"`
		Token     string    `gorm:"column:token"`
		ExpiresAt time.Time `gorm:"column:expires_at"`
		IsUsed    bool      `gorm:"column:is_used"`
	}

	result := r.db.Table("branch_auth_tokens").Where("token = ?", token).First(&authToken)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, result.Error
	}

	if authToken.IsUsed {
		return false, nil
	}

	if authToken.ExpiresAt.Before(time.Now()) {
		return false, nil
	}

	return true, nil
}

// UseAuthToken utiliza um token de autenticação, invalidando-o após o uso
func (r *GormBranchRepository) UseAuthToken(token string) error {
	result := r.db.Table("branch_auth_tokens").Where("token = ?", token).Update("is_used", true)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("token não encontrado")
	}

	return nil
}

// GetBranchesByRegion retorna filiais por região (estado)
func (r *GormBranchRepository) GetBranchesByRegion(region string) ([]models.Branch, error) {
	var branches []models.Branch
	result := r.db.Where("state = ?", region).Find(&branches)
	return branches, result.Error
}

// GetActiveBranches retorna todas as filiais ativas
func (r *GormBranchRepository) GetActiveBranches() ([]models.Branch, error) {
	var branches []models.Branch
	result := r.db.Where("is_active = ?", true).Find(&branches)
	return branches, result.Error
}

// GetBranchMetrics retorna métricas das filiais
func (r *GormBranchRepository) GetBranchMetrics() (*models.BranchMetrics, error) {
	var metrics models.BranchMetrics

	// Total de filiais
	if err := r.db.Model(&models.Branch{}).Count(&metrics.TotalBranches).Error; err != nil {
		return nil, err
	}

	// Filiais ativas
	if err := r.db.Model(&models.Branch{}).Where("is_active = ?", true).Count(&metrics.ActiveBranches).Error; err != nil {
		return nil, err
	}

	// Filiais por região
	type RegionCount struct {
		Region string
		Count  int64
	}
	var regionCounts []RegionCount
	if err := r.db.Model(&models.Branch{}).
		Select("state as Region, count(*) as Count").
		Group("state").
		Find(&regionCounts).Error; err != nil {
		return nil, err
	}

	metrics.BranchesByRegion = make(map[string]int64)
	for _, rc := range regionCounts {
		metrics.BranchesByRegion[rc.Region] = rc.Count
	}

	return &metrics, nil
}

// GetBranchSummaries retorna resumos de todas as filiais
func (r *GormBranchRepository) GetBranchSummaries() ([]models.BranchSummary, error) {
	var branches []models.Branch
	if err := r.db.Find(&branches).Error; err != nil {
		return nil, err
	}

	summaries := make([]models.BranchSummary, len(branches))
	for i, branch := range branches {
		summaries[i] = branch.ToSummary()
	}

	return summaries, nil
}

// GetBranchSummaryByID retorna o resumo de uma filial pelo ID
func (r *GormBranchRepository) GetBranchSummaryByID(id uint) (*models.BranchSummary, error) {
	branch, err := r.FindByID(context.Background(), id) // Corrigido para FindByID e adicionado context.Background()
	if err != nil {
		return nil, err
	}

	if branch == nil {
		return nil, nil
	}

	summary := branch.ToSummary()
	return &summary, nil
}
