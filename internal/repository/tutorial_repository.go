package repository

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	"tradicao/internal/models"
)

// TutorialRepository define as operações de banco de dados para tutoriais
type TutorialRepository interface {
	// Operações básicas
	Create(tutorial *models.Tutorial) error
	FindByID(id uint) (*models.Tutorial, error)
	Update(tutorial *models.Tutorial) error
	Delete(id uint) error
	
	// Listagem e busca
	List(filters TutorialFilters, offset, limit int) ([]models.Tutorial, int64, error)
	FindByEquipamento(equipamentoID uint, ativo bool) ([]models.Tutorial, error)
	FindByFilial(filialID uint, ativo bool) ([]models.Tutorial, error)
	FindByTipo(tipo models.TipoTutorial, ativo bool) ([]models.Tutorial, error)
	FindByNivel(nivel models.NivelDificuldade, ativo bool) ([]models.Tutorial, error)
	Search(query string, limit int) ([]models.Tutorial, error)
	
	// Estatísticas
	CountByTipo() (map[models.TipoTutorial]int64, error)
	CountByNivel() (map[models.NivelDificuldade]int64, error)
	
	// Visualizações e favoritos
	IncrementarVisualizacao(tutorialID uint) error
	AdicionarFavorito(usuarioID, tutorialID uint) error
	RemoverFavorito(usuarioID, tutorialID uint) error
	ListarFavoritos(usuarioID uint) ([]models.Tutorial, error)
	VerificarFavorito(usuarioID, tutorialID uint) (bool, error)
}

// TutorialFilters contém os filtros para busca de tutoriais
type TutorialFilters struct {
	Termo           string
	Tipo            models.TipoTutorial
	Nivel           models.NivelDificuldade
	EquipamentoID   uint
	FilialID        uint
	ApenasAtivos    bool
	OrdenarPor      string
	Ordem           string // "asc" ou "desc"
}

type tutorialRepository struct {
	db *gorm.DB
}

// NewTutorialRepository cria uma nova instância do repositório de tutoriais
func NewTutorialRepository(db *gorm.DB) TutorialRepository {
	return &tutorialRepository{db: db}
}

// Create cria um novo tutorial
func (r *tutorialRepository) Create(tutorial *models.Tutorial) error {
	return r.db.Create(tutorial).Error
}

// FindByID busca um tutorial pelo ID
func (r *tutorialRepository) FindByID(id uint) (*models.Tutorial, error) {
	var tutorial models.Tutorial
	err := r.db.Preload("Equipamento").
		Preload("Filial").
		Preload("Autor").
		First(&tutorial, id).Error

	if err != nil {
		return nil, err
	}

	// Incrementa o contador de visualizações
	go r.IncrementarVisualizacao(id)

	return &tutorial, nil
}

// Update atualiza um tutorial existente
func (r *tutorialRepository) Update(tutorial *models.Tutorial) error {
	return r.db.Save(tutorial).Error
}

// Delete remove um tutorial
func (r *tutorialRepository) Delete(id uint) error {
	// Soft delete
	return r.db.Delete(&models.Tutorial{}, id).Error
}

// List retorna uma lista de tutoriais com base nos filtros fornecidos
func (r *tutorialRepository) List(filters TutorialFilters, offset, limit int) ([]models.Tutorial, int64, error) {
	var tutorials []models.Tutorial
	var total int64
	
	query := r.db.Model(&models.Tutorial{}).Preload("Equipamento").Preload("Filial").Preload("Autor")
	
	// Aplicar filtros
	if filters.ApenasAtivos {
		query = query.Where("ativo = ?", true)
	}
	
	if filters.Tipo != "" {
		query = query.Where("tipo = ?", filters.Tipo)
	}
	
	if filters.Nivel != "" {
		query = query.Where("nivel = ?", filters.Nivel)
	}
	
	if filters.EquipamentoID > 0 {
		query = query.Where("equipamento_id = ?", filters.EquipamentoID)
	}
	
	if filters.FilialID > 0 {
		query = query.Where("filial_id = ?", filters.FilialID)
	}
	
	if filters.Termo != "" {
		termo := "%" + strings.ToLower(filters.Termo) + "%"
		query = query.Where("LOWER(titulo) LIKE ? OR LOWER(descricao) LIKE ? OR LOWER(conteudo) LIKE ?", 
			termo, termo, termo)
	}
	
	// Ordenação
	orderBy := "created_at"
	if filters.OrdenarPor != "" {
		orderBy = filters.OrdenarPor
	}
	
	orderDirection := "DESC"
	if filters.Ordem != "" {
		orderDirection = strings.ToUpper(filters.Ordem)
		if orderDirection != "ASC" && orderDirection != "DESC" {
			orderDirection = "DESC"
		}
	}
	
	// Contar total de registros
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// Aplicar paginação e ordenação
	err := query.Order(fmt.Sprintf("%s %s", orderBy, orderDirection)).
		Offset(offset).
		Limit(limit).
		Find(&tutorials).Error
		
	if err != nil {
		return nil, 0, err
	}
	
	return tutorials, total, nil
}

// FindByEquipamento busca tutoriais por equipamento
func (r *tutorialRepository) FindByEquipamento(equipamentoID uint, ativo bool) ([]models.Tutorial, error) {
	var tutoriais []models.Tutorial
	query := r.db.Where("equipamento_id = ?", equipamentoID)
	
	if ativo {
		query = query.Where("ativo = ?", true)
	}
	
	err := query.Order("titulo ASC").Find(&tutoriais).Error
	return tutoriais, err
}

// FindByFilial busca tutoriais por filial
func (r *tutorialRepository) FindByFilial(filialID uint, ativo bool) ([]models.Tutorial, error) {
	var tutoriais []models.Tutorial
	query := r.db.Where("filial_id = ?", filialID)
	
	if ativo {
		query = query.Where("ativo = ?", true)
	}
	
	err := query.Order("titulo ASC").Find(&tutoriais).Error
	return tutoriais, err
}

// FindByTipo busca tutoriais por tipo
func (r *tutorialRepository) FindByTipo(tipo models.TipoTutorial, ativo bool) ([]models.Tutorial, error) {
	var tutoriais []models.Tutorial
	query := r.db.Where("tipo = ?", tipo)
	
	if ativo {
		query = query.Where("ativo = ?", true)
	}
	
	err := query.Order("titulo ASC").Find(&tutoriais).Error
	return tutoriais, err
}

// FindByNivel busca tutoriais por nível de dificuldade
func (r *tutorialRepository) FindByNivel(nivel models.NivelDificuldade, ativo bool) ([]models.Tutorial, error) {
	var tutoriais []models.Tutorial
	query := r.db.Where("nivel = ?", nivel)
	
	if ativo {
		query = query.Where("ativo = ?", true)
	}
	
	err := query.Order("titulo ASC").Find(&tutoriais).Error
	return tutoriais, err
}

// Search realiza uma busca por termos nos tutoriais
func (r *tutorialRepository) Search(query string, limit int) ([]models.Tutorial, error) {
	var tutoriais []models.Tutorial
	termo := "%" + strings.ToLower(query) + "%"
	
	err := r.db.Where("LOWER(titulo) LIKE ? OR LOWER(descricao) LIKE ? OR LOWER(conteudo) LIKE ?", 
		termo, termo, termo).
		Where("ativo = ?", true).
		Order("titulo ASC").
		Limit(limit).
		Find(&tutoriais).Error
		
	return tutoriais, err
}

// CountByTipo retorna a contagem de tutoriais por tipo
func (r *tutorialRepository) CountByTipo() (map[models.TipoTutorial]int64, error) {
	type Result struct {
		Tipo  models.TipoTutorial
		Total int64
	}
	
	var results []Result
	err := r.db.Model(&models.Tutorial{}).
		Select("tipo, count(*) as total").
		Where("ativo = ?", true).
		Group("tipo").
		Scan(&results).Error
		
	if err != nil {
		return nil, err
	}
	
	// Converter para mapa
	contagem := make(map[models.TipoTutorial]int64)
	for _, r := range results {
		contagem[r.Tipo] = r.Total
	}
	
	return contagem, nil
}

// CountByNivel retorna a contagem de tutoriais por nível de dificuldade
func (r *tutorialRepository) CountByNivel() (map[models.NivelDificuldade]int64, error) {
	type Result struct {
		Nivel models.NivelDificuldade
		Total int64
	}
	
	var results []Result
	err := r.db.Model(&models.Tutorial{}).
		Select("nivel, count(*) as total").
		Where("ativo = ?", true).
		Group("nivel").
		Scan(&results).Error
		
	if err != nil {
		return nil, err
	}
	
	// Converter para mapa
	contagem := make(map[models.NivelDificuldade]int64)
	for _, r := range results {
		contagem[r.Nivel] = r.Total
	}
	
	return contagem, nil
}

// IncrementarVisualizacao incrementa o contador de visualizações de um tutorial
func (r *tutorialRepository) IncrementarVisualizacao(tutorialID uint) error {
	return r.db.Model(&models.Tutorial{}).
		Where("id = ?", tutorialID).
		Update("visualizacoes", gorm.Expr("visualizacoes + 1")).Error
}

// AdicionarFavorito adiciona um tutorial aos favoritos do usuário
func (r *tutorialRepository) AdicionarFavorito(usuarioID, tutorialID uint) error {
	// Verificar se já está favoritado
	existe, err := r.VerificarFavorito(usuarioID, tutorialID)
	if err != nil {
		return err
	}
	
	if existe {
		return nil // Já está favoritado
	}
	
	favorito := &models.TutorialFavorito{
		UsuarioID:  usuarioID,
		TutorialID: tutorialID,
		CreatedAt:  time.Now(),
	}
	
	return r.db.Create(favorito).Error
}

// RemoverFavorito remove um tutorial dos favoritos do usuário
func (r *tutorialRepository) RemoverFavorito(usuarioID, tutorialID uint) error {
	return r.db.Where("usuario_id = ? AND tutorial_id = ?", usuarioID, tutorialID).
		Delete(&models.TutorialFavorito{}).Error
}

// ListarFavoritos retorna a lista de tutoriais favoritos do usuário
func (r *tutorialRepository) ListarFavoritos(usuarioID uint) ([]models.Tutorial, error) {
	var tutoriais []models.Tutorial
	
	err := r.db.Joins("JOIN tutoriais_favoritos ON tutoriais.id = tutoriais_favoritos.tutorial_id").
		Where("tutoriais_favoritos.usuario_id = ?", usuarioID).
		Where("tutoriais.ativo = ?", true).
		Order("tutoriais.titulo ASC").
		Find(&tutoriais).Error
		
	return tutoriais, err
}

// VerificarFavorito verifica se um tutorial está nos favoritos do usuário
func (r *tutorialRepository) VerificarFavorito(usuarioID, tutorialID uint) (bool, error) {
	var count int64
	err := r.db.Model(&models.TutorialFavorito{}).
		Where("usuario_id = ? AND tutorial_id = ?", usuarioID, tutorialID).
		Count(&count).Error
		
	return count > 0, err
}
