package tutorial

import (
	"context"
	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// TutorialAgent é responsável pela lógica de tutoriais
// (CRUD, validação, integração com repositório)
type TutorialAgent struct {
	repo repository.TutorialRepository
}

// NewTutorialAgent cria uma nova instância do TutorialAgent
func NewTutorialAgent(repo repository.TutorialRepository) *TutorialAgent {
	return &TutorialAgent{repo: repo}
}

func (a *TutorialAgent) CreateTutorial(ctx context.Context, tutorial *models.Tutorial) error {
	// Validação básica
	if tutorial.Titulo == "" || tutorial.Conteudo == "" {
		return ErrInvalidTutorial
	}
	return a.repo.Create(tutorial)
}

func (a *TutorialAgent) GetTutorialByID(ctx context.Context, id uint) (*models.Tutorial, error) {
	return a.repo.FindByID(id)
}

func (a *TutorialAgent) UpdateTutorial(ctx context.Context, tutorial *models.Tutorial) error {
	if tutorial.ID == 0 {
		return ErrInvalidTutorial
	}
	return a.repo.Update(tutorial)
}

func (a *TutorialAgent) DeleteTutorial(ctx context.Context, id uint) error {
	return a.repo.Delete(id)
}

func (a *TutorialAgent) ListTutorials(ctx context.Context, filters repository.TutorialFilters, offset, limit int) ([]models.Tutorial, int64, error) {
	return a.repo.List(filters, offset, limit)
}

// ErrInvalidTutorial representa erro de validação
var ErrInvalidTutorial = repository.ErrInvalidID
