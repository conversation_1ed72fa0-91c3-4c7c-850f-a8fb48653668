package services

import (
	"context"
	"errors"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// TutorialService define a interface para o serviço de tutoriais
type TutorialService interface {
	// Operações básicas
	CreateTutorial(ctx context.Context, tutorial *models.Tutorial) error
	GetTutorialByID(ctx context.Context, id uint) (*models.Tutorial, error)
	UpdateTutorial(ctx context.Context, tutorial *models.Tutorial) error
	DeleteTutorial(ctx context.Context, id uint) error
	ListTutorials(ctx context.Context, filters repository.TutorialFilters, offset, limit int) ([]models.Tutorial, int64, error)
	
	// Operações específicas
	IncrementarVisualizacao(ctx context.Context, tutorialID uint) error
	AdicionarFavorito(ctx context.Context, usuarioID, tutorialID uint) error
	RemoverFavorito(ctx context.Context, usuarioID, tutorialID uint) error
	ListarFavoritos(ctx context.Context, usuarioID uint) ([]models.Tutorial, error)
	VerificarFavorito(ctx context.Context, usuarioID, tutorialID uint) (bool, error)
	
	// Estatísticas
	ObterEstatisticas(ctx context.Context) (*TutorialStats, error)
}

// TutorialStats contém estatísticas sobre os tutoriais
type TutorialStats struct {
	TotalTutoriais  int64
	TutoriaisPorTipo map[models.TipoTutorial]int64
	TutoriaisPorNivel map[models.NivelDificuldade]int64
}

type tutorialService struct {
	repo repository.TutorialRepository
}

// NewTutorialService cria uma nova instância do serviço de tutoriais
func NewTutorialService(repo repository.TutorialRepository) TutorialService {
	return &tutorialService{
		repo: repo,
	}
}

func (s *tutorialService) CreateTutorial(ctx context.Context, tutorial *models.Tutorial) error {
	// Validação básica
	if tutorial.Titulo == "" || tutorial.Conteudo == "" {
		return errors.New("título e conteúdo são obrigatórios")
	}
	
	// Configura dados iniciais
	now := time.Now()
	tutorial.CreatedAt = now
	tutorial.UpdatedAt = now
	tutorial.Visualizacoes = 0
	tutorial.Avaliacao = 0
	tutorial.Ativo = true
	
	return s.repo.Create(tutorial)
}

func (s *tutorialService) GetTutorialByID(ctx context.Context, id uint) (*models.Tutorial, error) {
	if id == 0 {
		return nil, errors.New("ID do tutorial é obrigatório")
	}
	
	tutorial, err := s.repo.FindByID(id)
	if err != nil {
		return nil, err
	}
	
	// Incrementa o contador de visualizações de forma assíncrona
	go func() {
		_ = s.repo.IncrementarVisualizacao(id)
	}()
	
	return tutorial, nil
}

func (s *tutorialService) UpdateTutorial(ctx context.Context, tutorial *models.Tutorial) error {
	if tutorial.ID == 0 {
		return errors.New("ID do tutorial é obrigatório")
	}
	
	// Atualiza a data de atualização
	tutorial.UpdatedAt = time.Now()
	
	return s.repo.Update(tutorial)
}

func (s *tutorialService) DeleteTutorial(ctx context.Context, id uint) error {
	if id == 0 {
		return errors.New("ID do tutorial é obrigatório")
	}
	
	// Verifica se o tutorial existe
	_, err := s.repo.FindByID(id)
	if err != nil {
		return err
	}
	
	return s.repo.Delete(id)
}

func (s *tutorialService) ListTutorials(ctx context.Context, filters repository.TutorialFilters, offset, limit int) ([]models.Tutorial, int64, error) {
	// Garante valores padrão para paginação
	if limit <= 0 {
		limit = 10
	}
	if offset < 0 {
		offset = 0
	}
	
	return s.repo.List(filters, offset, limit)
}

func (s *tutorialService) IncrementarVisualizacao(ctx context.Context, tutorialID uint) error {
	if tutorialID == 0 {
		return errors.New("ID do tutorial é obrigatório")
	}
	
	return s.repo.IncrementarVisualizacao(tutorialID)
}

func (s *tutorialService) AdicionarFavorito(ctx context.Context, usuarioID, tutorialID uint) error {
	if usuarioID == 0 || tutorialID == 0 {
		return errors.New("ID do usuário e do tutorial são obrigatórios")
	}
	
	// Verifica se o tutorial existe
	_, err := s.repo.FindByID(tutorialID)
	if err != nil {
		return errors.New("tutorial não encontrado")
	}
	
	return s.repo.AdicionarFavorito(usuarioID, tutorialID)
}

func (s *tutorialService) RemoverFavorito(ctx context.Context, usuarioID, tutorialID uint) error {
	if usuarioID == 0 || tutorialID == 0 {
		return errors.New("ID do usuário e do tutorial são obrigatórios")
	}
	
	return s.repo.RemoverFavorito(usuarioID, tutorialID)
}

func (s *tutorialService) ListarFavoritos(ctx context.Context, usuarioID uint) ([]models.Tutorial, error) {
	if usuarioID == 0 {
		return nil, errors.New("ID do usuário é obrigatório")
	}
	
	return s.repo.ListarFavoritos(usuarioID)
}

func (s *tutorialService) VerificarFavorito(ctx context.Context, usuarioID, tutorialID uint) (bool, error) {
	if usuarioID == 0 || tutorialID == 0 {
		return false, errors.New("ID do usuário e do tutorial são obrigatórios")
	}
	
	return s.repo.VerificarFavorito(usuarioID, tutorialID)
}

func (s *tutorialService) ObterEstatisticas(ctx context.Context) (*TutorialStats, error) {
	// Obtém a contagem total de tutoriais ativos
	filters := repository.TutorialFilters{ApenasAtivos: true}
	_, total, err := s.repo.List(filters, 0, 1) // Apenas para contar o total
	if err != nil {
		return nil, err
	}
	
	// Obtém a contagem por tipo
	porTipo, err := s.repo.CountByTipo()
	if err != nil {
		return nil, err
	}
	
	// Obtém a contagem por nível
	porNivel, err := s.repo.CountByNivel()
	if err != nil {
		return nil, err
	}
	
	return &TutorialStats{
		TotalTutoriais:  total,
		TutoriaisPorTipo: porTipo,
		TutoriaisPorNivel: porNivel,
	}, nil
}
