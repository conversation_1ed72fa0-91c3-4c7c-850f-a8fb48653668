package cache

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestPermissionCache(t *testing.T) {
	// Configuração do cache com TTL curto para testes
	cacheTTL := 2 * time.Second
	cleanupInterval := 5 * time.Minute
	pc := NewPermissionCache(cacheTTL, cleanupInterval)

	// Dados de teste
	technicianID := uint(1)
	orderID := uint(10)


	t.Run("TestSetAndGetPermission", func(t *testing.T) {
		// Testa a definição e obtenção de permissão
		pc.SetPermission(technicianID, orderID, true)
		
		// Verifica se a permissão foi armazenada corretamente
		permission, found := pc.GetPermission(technicianID, orderID)
		assert.True(t, found, "Deveria encontrar a permissão no cache")
		assert.True(t, permission, "A permissão deveria ser verdadeira")
	})

	t.Run("TestPermissionExpiration", func(t *testing.T) {
		// Testa a expiração do cache
		pc.SetPermission(technicianID, orderID+1, true)
		
		// Aguarda o cache expirar
		time.Sleep(cacheTTL + 100*time.Millisecond)
		
		// Verifica se a permissão expirou
		_, found := pc.GetPermission(technicianID, orderID+1)
		assert.False(t, found, "A permissão deveria ter expirado")
	})

	t.Run("TestInvalidatePermission", func(t *testing.T) {
		// Testa a invalidação de uma permissão específica
		pc.SetPermission(technicianID, orderID+2, true)
		pc.InvalidatePermission(technicianID, orderID+2)
		
		// Verifica se a permissão foi removida
		_, found := pc.GetPermission(technicianID, orderID+2)
		assert.False(t, found, "A permissão deveria ter sido removida")
	})

	t.Run("TestInvalidateTechnician", func(t *testing.T) {
		// Adiciona várias permissões para o mesmo técnico
		for i := 0; i < 3; i++ {
			pc.SetPermission(technicianID+1, orderID+uint(i), true)
		}
		
		// Invalida todas as permissões do técnico
		pc.InvalidateTechnician(technicianID + 1)
		
		// Verifica se todas as permissões foram removidas
		for i := 0; i < 3; i++ {
			_, found := pc.GetPermission(technicianID+1, orderID+uint(i))
			assert.False(t, found, "Todas as permissões do técnico deveriam ter sido removidas")
		}
	})

	t.Run("TestInvalidateOrder", func(t *testing.T) {
		// Adiciona várias permissões para a mesma ordem
		for i := 0; i < 3; i++ {
			pc.SetPermission(technicianID+uint(i), orderID+100, true)
		}
		
		// Invalida todas as permissões da ordem
		pc.InvalidateOrder(orderID + 100)
		
		// Verifica se todas as permissões foram removidas
		for i := 0; i < 3; i++ {
			_, found := pc.GetPermission(technicianID+uint(i), orderID+100)
			assert.False(t, found, "Todas as permissões da ordem deveriam ter sido removidas")
		}
	})
}
