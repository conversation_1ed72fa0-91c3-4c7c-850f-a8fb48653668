package cache

import (
	"fmt"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
)

// PermissionCache gerencia o cache de permissões
type PermissionCache struct {
	cache *cache.Cache
	mutex sync.RWMutex
}

// NewPermissionCache cria uma nova instância do cache de permissões
func NewPermissionCache(defaultTTL, cleanupInterval time.Duration) *PermissionCache {
	return &PermissionCache{
		cache: cache.New(defaultTTL, cleanupInterval),
	}
}

// GetPermission verifica se um técnico tem permissão para acessar uma ordem
func (pc *PermissionCache) GetPermission(technicianID, orderID uint) (bool, bool) {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()

	key := pc.generateKey(technicianID, orderID)
	if cached, found := pc.cache.Get(key); found {
		return cached.(bool), true
	}
	return false, false
}

// SetPermission armazena a permissão no cache
func (pc *PermissionCache) SetPermission(technicianID, orderID uint, hasPermission bool) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	key := pc.generateKey(technicianID, orderID)
	pc.cache.Set(key, hasPermission, cache.DefaultExpiration)
}

// InvalidatePermission remove uma permissão específica do cache
func (pc *PermissionCache) InvalidatePermission(technicianID, orderID uint) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	key := pc.generateKey(technicianID, orderID)
	pc.cache.Delete(key)
}

// InvalidateTechnician remove todas as permissões de um técnico do cache
func (pc *PermissionCache) InvalidateTechnician(technicianID uint) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	// Percorre todos os itens do cache e remove os que pertencem ao técnico
	items := pc.cache.Items()
	prefix := fmt.Sprintf("perm:%d:", technicianID)

	for key := range items {
		if len(key) > len(prefix) && key[:len(prefix)] == prefix {
			pc.cache.Delete(key)
		}
	}
}

// InvalidateOrder remove todas as permissões relacionadas a uma ordem do cache
func (pc *PermissionCache) InvalidateOrder(orderID uint) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	// Percorre todos os itens do cache e remove os que pertencem à ordem
	items := pc.cache.Items()
	suffix := fmt.Sprintf(":%d", orderID)

	for key := range items {
		keyStr := key.(string)
		if len(keyStr) > len(suffix) && keyStr[len(keyStr)-len(suffix):] == suffix {
			pc.cache.Delete(key)
		}
	}
}

// generateKey gera uma chave única para o cache
func (pc *PermissionCache) generateKey(technicianID, orderID uint) string {
	return fmt.Sprintf("perm:%d:%d", technicianID, orderID)
}
