package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"tradicao/internal/models"
	"tradicao/internal/repository"
	"tradicao/internal/services"
)

// TutorialHandler lida com requisições HTTP relacionadas a tutoriais
type TutorialHandler struct {
	service services.TutorialService
}

// NewTutorialHandler cria uma nova instância do TutorialHandler
func NewTutorialHandler(service services.TutorialService) *TutorialHandler {
	return &TutorialHandler{service: service}
}

// RegisterRoutes registra as rotas do handler
func (h *TutorialHandler) RegisterRoutes(router *gin.Engine) {
	// Grupo de rotas da API
	api := router.Group("/api/tutoriais")
	{
		api.GET("", h.ListTutoriais)
		api.GET("/:id", h.GetTutorial)
		api.POST("", h.CreateTutorial)
		api.PUT("/:id", h.UpdateTutorial)
		api.DELETE("/:id", h.DeleteTutorial)
		
		// Favoritos
		api.POST("/:id/favorito", h.AdicionarFavorito)
		api.DELETE("/:id/favorito", h.RemoverFavorito)
		api.GET("/favoritos", h.ListarFavoritos)
		
		// Estatísticas
		api.GET("/estatisticas", h.ObterEstatisticas)
	}
}

// CreateTutorial lida com a criação de um novo tutorial
func (h *TutorialHandler) CreateTutorial(c *gin.Context) {
	var tutorial models.Tutorial
	if err := c.ShouldBindJSON(&tutorial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	// Obtém o ID do usuário autenticado (exemplo usando middleware de autenticação)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Não autorizado"})
		return
	}

	// Define o autor do tutorial
	tutorial.AutorID = userID.(uint)

	if err := h.service.CreateTutorial(c.Request.Context(), &tutorial); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao criar tutorial: " + err.Error()})
		return
	}

	c.JSON(http.StatusCreated, tutorial)
}

// GetTutorial obtém um tutorial pelo ID
func (h *TutorialHandler) GetTutorial(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	tutorial, err := h.service.GetTutorialByID(c.Request.Context(), uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Tutorial não encontrado"})
		return
	}

	c.JSON(http.StatusOK, tutorial)
}

// UpdateTutorial atualiza um tutorial existente
func (h *TutorialHandler) UpdateTutorial(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var tutorial models.Tutorial
	if err := c.ShouldBindJSON(&tutorial); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dados inválidos"})
		return
	}

	// Garante que o ID da URL seja usado
	tutorial.ID = uint(id)

	if err := h.service.UpdateTutorial(c.Request.Context(), &tutorial); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar tutorial: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, tutorial)
}

// DeleteTutorial remove um tutorial
func (h *TutorialHandler) DeleteTutorial(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := h.service.DeleteTutorial(c.Request.Context(), uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao excluir tutorial: " + err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListTutoriais lista tutoriais com filtros e paginação
func (h *TutorialHandler) ListTutoriais(c *gin.Context) {
	// Parâmetros de paginação
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// Filtros
	var filters repository.TutorialFilters
	
	if tipo := c.Query("tipo"); tipo != "" {
		filters.Tipo = models.TipoTutorial(tipo)
	}
	
	if nivel := c.Query("nivel"); nivel != "" {
		filters.Nivel = models.NivelDificuldade(nivel)
	}
	
	if ativo := c.Query("ativo"); ativo != "" {
		filters.ApenasAtivos = ativo == "true"
	}
	
	if termo := c.Query("q"); termo != "" {
		filters.Termo = termo
	}
	
	// Ordenação
	filters.OrdenarPor = c.DefaultQuery("ordenar_por", "created_at")
	filters.Ordem = c.DefaultQuery("ordem", "desc")

	tutoriais, total, err := h.service.ListTutorials(c.Request.Context(), filters, offset, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar tutoriais: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": tutoriais,
		"total": total,
		"offset": offset,
		"limit": limit,
	})
}

// AdicionarFavorito adiciona um tutorial aos favoritos do usuário
func (h *TutorialHandler) AdicionarFavorito(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Não autorizado"})
		return
	}

	tutorialID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do tutorial inválido"})
		return
	}

	if err := h.service.AdicionarFavorito(c.Request.Context(), userID.(uint), uint(tutorialID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao adicionar favorito: " + err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// RemoverFavorito remove um tutorial dos favoritos do usuário
func (h *TutorialHandler) RemoverFavorito(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Não autorizado"})
		return
	}

	tutorialID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "ID do tutorial inválido"})
		return
	}

	if err := h.service.RemoverFavorito(c.Request.Context(), userID.(uint), uint(tutorialID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao remover favorito: " + err.Error()})
		return
	}

	c.Status(http.StatusNoContent)
}

// ListarFavoritos lista os tutoriais favoritos do usuário
func (h *TutorialHandler) ListarFavoritos(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Não autorizado"})
		return
	}

	tutoriais, err := h.service.ListarFavoritos(c.Request.Context(), userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao listar favoritos: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, tutoriais)
}

// ObterEstatisticas retorna estatísticas sobre os tutoriais
func (h *TutorialHandler) ObterEstatisticas(c *gin.Context) {
	stats, err := h.service.ObterEstatisticas(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao obter estatísticas: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// DTOs para validação de entrada
type createTutorialRequest struct {
	Titulo        string                `json:"titulo" binding:"required"`
	Descricao     string                `json:"descricao"`
	Conteudo      string                `json:"conteudo" binding:"required"`
	Tipo          models.TipoTutorial   `json:"tipo" binding:"required,oneof=manutencao operacao seguranca"`
	Nivel         models.NivelDificuldade `json:"nivel" binding:"required,oneof=iniciante intermediario avancado"`
	Categoria     string                `json:"categoria"`
	DuracaoMinutos int                   `json:"duracao_minutos"`
	ImagemURL     string                `json:"imagem_url"`
	VideoURL      string                `json:"video_url"`
	EquipamentoID *uint                 `json:"equipamento_id"`
	FilialID      *uint                 `json:"filial_id"`
	Tags          string                `json:"tags"`
}

type updateTutorialRequest struct {
	Titulo        *string                `json:"titulo"`
	Descricao     *string                `json:"descricao"`
	Conteudo      *string                `json:"conteudo"`
	Tipo          *models.TipoTutorial   `json:"tipo"`
	Nivel         *models.NivelDificuldade `json:"nivel"`
	Categoria     *string                `json:"categoria"`
	DuracaoMinutos *int                   `json:"duracao_minutos"`
	ImagemURL     *string                `json:"imagem_url"`
	VideoURL      *string                `json:"video_url"`
	EquipamentoID *uint                  `json:"equipamento_id"`
	FilialID      *uint                  `json:"filial_id"`
	Ativo         *bool                  `json:"ativo"`
	Tags          *string                `json:"tags"`
}
