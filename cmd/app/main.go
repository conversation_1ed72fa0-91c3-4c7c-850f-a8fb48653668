package main

import (
	"log"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"tradicao/internal/config"
	"tradicao/internal/handlers"
	"tradicao/internal/repository"
	"tradicao/internal/routes"
	"tradicao/internal/services"
)

func main() {
	// Carrega a configuração
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Erro ao carregar configuração: %v", err)
	}

	// Inicializa o banco de dados
	db, err := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Inicializa o repositório de usuários
	userRepo := repository.NewGormUserRepository()

	// Inicializa os serviços
	authService := services.NewAuthService(userRepo, cfg)
	userService := services.NewUserService(userRepo, cfg)

	// Inicializa os handlers
	authHandler := handlers.NewAuthHandler(userService, authService)

	// Configura o router
	router := gin.Default()

	// Configura o diretório de templates
	templates, err := filepath.Glob("web/templates/**/*.html")
	if err != nil {
		log.Fatalf("Erro ao carregar templates: %v", err)
	}
	log.Printf("Templates carregados: %v", templates)
	router.LoadHTMLGlob("web/templates/**/*.html")

	// Configura o diretório de arquivos estáticos
	router.Static("/static", "./web/static")

	// Configura as rotas
	routes.SetupAuthRoutes(router, authHandler, authService)
	routes.SetupTutorialRoutes(router, db, authService)

	// Inicia o servidor
	if err := router.Run(":8080"); err != nil {
		log.Fatalf("Erro ao iniciar servidor: %v", err)
	}
}
