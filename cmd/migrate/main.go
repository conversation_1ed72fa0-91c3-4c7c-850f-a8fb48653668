package main

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"tradicao/internal/config"
	"tradicao/internal/models"
)

func main() {
	// Carrega a configuração
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Erro ao carregar configuração: %v", err)
	}

	// Conecta ao banco de dados
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		cfg.DBHost, cfg.DBPort, cfg.DBUser, cfg.DBPass, cfg.DBName)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados: %v", err)
	}

	// Executa as migrações
	err = db.AutoMigrate(
		&models.User{},
	)
	if err != nil {
		log.Fatalf("Erro ao executar migrações: %v", err)
	}

	log.Println("Migrações executadas com sucesso!")
}
