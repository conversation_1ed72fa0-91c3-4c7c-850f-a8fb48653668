package main

import (
	"context"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"flag"
	"tradicao/internal/config"
	"tradicao/internal/controllers"
	"tradicao/internal/database"
	"tradicao/internal/ent"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/models"
	"tradicao/internal/permissions"
	"tradicao/internal/repository"
	"tradicao/internal/routes"
	"tradicao/internal/services"
	"tradicao/web/templates"
)

func main() {
	// Configurar flags de linha de comando
	port := flag.Int("port", 8080, "Porta para executar o servidor")
	flag.Parse()

	// Configuração de logs
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)

	// Usar o diretório atual ecomo diretório de trabalho
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("Erro ao obter diretório de trabalho atual: %v", err)
	}
	log.Printf("Usando diretório de trabalho: %s", workDir)

	// Carrega configurações
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Erro ao carregar configurações: %v", err)
	}

	// Configurar o roteador Gin
	router := gin.New()

	// Configurar o modo de release para produção
	if os.Getenv("GIN_MODE") == "release" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// Configurar middlewares
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Configurar proxy confiável
	router.SetTrustedProxies([]string{"127.0.0.1"})

	// Configurar arquivos estáticos
	router.Static("/static", "./web/static")

	// Unificar todas as funções de template em um único FuncMap
	funcMap := template.FuncMap{
		"formatDateTime": func(t time.Time) string {
			return t.Format("02/01/2006 15:04")
		},
		"formatDate": func(t time.Time) string {
			return t.Format("02/01/2006")
		},
		"formatCurrency": func(value float64) string {
			return fmt.Sprintf("R$ %.2f", value)
		},
		"add": func(a, b int) int {
			return a + b
		},
		"sub": func(a, b int) int {
			return a - b
		},
		"mul": func(a, b int) int {
			return a * b
		},
		"upper": strings.ToUpper,
		"div": func(a, b int) int {
			if b == 0 {
				return 0
			}
			return a / b
		},
		"defaultString": func(s, defaultValue string) string {
			if s == "" {
				return defaultValue
			}
			return s
		},
		"statusToClass": func(status string) string {
			switch status {
			case "pending":
				return "warning"
			case "in_progress":
				return "info"
			case "completed":
				return "success"
			case "cancelled":
				return "danger"
			default:
				return "secondary"
			}
		},
		"formatStatus": func(status string) string {
			switch status {
			case "pending":
				return "Pendente"
			case "in_progress":
				return "Em Andamento"
			case "completed":
				return "Concluído"
			case "cancelled":
				return "Cancelado"
			default:
				return status
			}
		},
		"formatPriority": func(priority string) string {
			switch priority {
			case "low":
				return "Baixa"
			case "medium":
				return "Média"
			case "high":
				return "Alta"
			case "critical":
				return "Crítica"
			default:
				return priority
			}
		},
		"default": func(value, defaultValue interface{}) interface{} {
			if value == nil {
				return defaultValue
			}

			// Verificar se é string vazia
			if str, ok := value.(string); ok && str == "" {
				return defaultValue
			}

			// Verificar se é zero numérico
			switch v := value.(type) {
			case int:
				if v == 0 {
					return defaultValue
				}
			case float64:
				if v == 0 {
					return defaultValue
				}
			}

			return value
		},
		"statusToIcon": func(status string) string {
			switch status {
			case "pending":
				return "bi-hourglass-split"
			case "in_progress":
				return "bi-gear-wide-connected"
			case "completed":
				return "bi-check-circle"
			case "cancelled":
				return "bi-x-circle"
			default:
				return "bi-question-circle"
			}
		},
		"priorityToIcon": func(priority string) string {
			switch priority {
			case "low":
				return "bi-arrow-down-circle"
			case "medium":
				return "bi-dash-circle"
			case "high":
				return "bi-arrow-up-circle"
			case "critical":
				return "bi-exclamation-triangle"
			default:
				return "bi-question-circle"
			}
		},
		"formatTime": func(t time.Time) string {
			return t.Format("15:04")
		},
		"float64": func(v interface{}) float64 {
			switch val := v.(type) {
			case float64:
				return val
			case float32:
				return float64(val)
			case int:
				return float64(val)
			case int64:
				return float64(val)
			case int32:
				return float64(val)
			case string:
				f, err := strconv.ParseFloat(val, 64)
				if err != nil {
					return 0
				}
				return f
			default:
				return 0
			}
		},
	}

	// Adicionar funções do pacote templates.TemplateFuncs()
	for name, fn := range templates.TemplateFuncs() {
		funcMap[name] = fn
	}

	// Criar template com as funções unificadas
	tmpl := template.New("").Funcs(funcMap)
	tmpl = template.Must(tmpl.ParseGlob("web/templates/**/*.html"))
	router.SetHTMLTemplate(tmpl)

	log.Println("Templates carregados com sucesso")

	// Inicializa o GORM
	db, err := database.Connect()
	if err != nil {
		log.Fatalf("Erro ao conectar ao banco de dados via GORM: %v", err)
	}

	// Adicionar o banco de dados ao middleware para estar disponível no contexto
	router.Use(func(c *gin.Context) {
		c.Set("db", db)
		c.Next()
	})

	// INICIALIZAR SISTEMA UNIFICADO DE PERMISSÕES
	log.Println("Inicializando sistema unificado de permissões...")
	if err := permissions.InitializeGlobalPermissions(db, "data/permissions.yaml"); err != nil {
		log.Printf("AVISO: Erro ao inicializar sistema de permissões: %v", err)
		log.Println("Continuando sem sistema de permissões...")
	}

	// Inicializa cliente ENT usando o pool de conexões existente
	err = ent.InitClientWithPool(gin.Mode() != gin.ReleaseMode)
	if err != nil {
		log.Fatalf("Erro ao inicializar cliente ENT: %v", err)
	}
	defer ent.CloseClient()

	// Executar migrações automáticas em ambiente de desenvolvimento
	// Apenas se não estiver em produção e se a flag de migrações estiver ativada
	if gin.Mode() != gin.ReleaseMode && os.Getenv("RUN_MIGRATIONS") == "true" {
		log.Println("Executando migrações automáticas do ENT...")
		if err := ent.RunMigrationsWithPool(context.Background()); err != nil {
			log.Printf("Aviso: Erro ao executar migrações automáticas: %v", err)
		}
	}

	// Repositórios
	// Inicializa repositórios com suporte a ENT
	userRepo := repository.NewGormUserRepository()
	// Estes repositórios serão substituídos por implementações ENT no futuro
	// Por enquanto, vamos manter apenas os que são usados diretamente
	notificationRepo := repository.NewGormNotificationRepository(db)

	// Repositório, serviço e handler para a tabela technician_orders
	// REMOVIDO - Funcionalidade consolidada no unified_order_handler
	// technicianOrderRepo := repository.NewTechnicianOrderRepository(db)
	// technicianOrderService := services.NewTechnicianOrderService(technicianOrderRepo)
	// technicianOrderHandler := handlers.NewTechnicianOrderHandler(technicianOrderService)

	// Nota: Aqui podemos registrar o cliente ENT nos repositórios que o suportam
	// Por enquanto, vamos manter a compatibilidade com os repositórios existentes

	// Serviços
	userService := services.NewUserService(userRepo, cfg)

	// Nota: Estes serviços precisarão ser adaptados para usar o ENT no futuro
	// Por enquanto, vamos usar stubs para manter a compatibilidade
	// TODO: Implementar adapters para os serviços existentes
	notificationService := services.NewNotificationService(
		notificationRepo,
		userRepo,
		nil, // orderRepo será implementado com ENT no futuro
		os.Getenv("VAPID_PUBLIC_KEY"),
		os.Getenv("VAPID_PRIVATE_KEY"),
		os.Getenv("VAPID_SUBJECT"),
	)

	// Repositório e serviço de filiais
	branchRepo := repository.NewGormBranchRepository(db)
	branchService := services.NewBranchService(branchRepo)

	// Handlers
	authService := services.NewAuthService(userRepo, cfg) // Movido e corrigido para usar userRepo
	authHandler := handlers.NewAuthHandler(userService, authService)

	// Nota: Estes handlers precisarão ser adaptados para usar o ENT no futuro
	// Por enquanto, vamos comentar a inicialização dos handlers que precisam de adaptação
	// TODO: Implementar adapters para os handlers existentes
	// Descomente e adapte as linhas abaixo quando os adapters estiverem prontos
	/*
		maintenanceHandler := handlers.NewMaintenanceHandler(service, notificationService)
		maintenanceOrderHandler := handlers.NewMaintenanceOrderHandler(service)
		orderHandler := handlers.NewOrderHandler(service)
		dashboardHandler := handlers.NewDashboardHandler(service, stationAdapter)
	*/

	// Repositórios, adaptadores e handlers antigos removidos
	// Substituídos pelo sistema unificado que cria seus próprios repositórios internamente
	// orderRepo := repository.NewMaintenanceOrderRepository(db) // Não mais necessário

	// Usando stubs temporários para os outros handlers
	maintenanceHandler := &handlers.MaintenanceHandler{}
	// maintenanceOrderHandler removido - funcionalidade consolidada no unified_order_handler
	dashboardHandler := &handlers.DashboardHandler{}
	_ = handlers.NewWebSocketHandler(notificationService) // WebSocket handler removido temporariamente

	// Novos handlers para API
	userAPIHandler := handlers.NewUserAPIHandler(userService)
	equipmentRepo := repository.NewEquipmentRepository(db)
	equipmentService := services.NewEquipmentService(equipmentRepo)
	equipmentAPIHandler := handlers.NewEquipmentAPIHandler(equipmentService)

	// Handler para filiais
	filialFilterService := services.NewFilialFilterService(db)
	_ = handlers.NewBranchHandler(branchService, filialFilterService) // BranchHandler removido temporariamente

	// Sistema de Anexos
	attachmentRepo := repository.NewGormAttachmentRepository(db)
	attachmentService := services.NewAttachmentService(attachmentRepo)
	attachmentHandler := handlers.NewAttachmentHandler(attachmentService)

	// Sistema de Métricas por Filial
	branchMetricsService := services.NewBranchMetricsService(db)
	branchMetricsHandler := handlers.NewBranchMetricsHandler(branchMetricsService)

	// Middleware de Vinculação Automática por Filial
	branchAutoAssignmentMiddleware := middleware.NewBranchAutoAssignmentMiddleware(db)

	// Rotas públicas
	router.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "geral/index.html", gin.H{
			"title": "Rede Tradição - Excelência em Serviços",
		})
	})

	router.GET("/acesso-negado", func(c *gin.Context) {
		c.HTML(http.StatusOK, "auth/acesso_negado.html", gin.H{
			"title": "Acesso Negado - Rede Tradição",
		})
	})

	public := router.Group("/")
	{
		public.GET("/login", handlers.LoginHandler)
		public.POST("/api/auth/login", authHandler.Login)
		public.POST("/api/auth/logout", handlers.LogoutAPIHandler) // Usando o novo handler de logout para API
		public.GET("/logout", handlers.LogoutHandler)              // Rota de logout para acesso direto via navegador
	}

	// Rotas protegidas - Autenticação base
	protected := router.Group("/")
	protected.Use(middleware.AuthMiddleware(authService))
	{
		// Grupo para páginas HTML com verificação de permissão de página
		pages := protected.Group("/")
		pages.Use(middleware.PageAccessMiddleware())
		pages.Use(middleware.FilialMiddleware()) // Adicionar middleware de filial para contexto
		{
			// Dashboard
			pages.GET("/dashboard", func(c *gin.Context) {
				// Gerar token CSRF
				csrfToken := fmt.Sprintf("csrf-%d-%s", time.Now().UnixNano(), c.GetString("userID"))

				c.HTML(http.StatusOK, "dashboard/dashboard_new.html", gin.H{
					"title":      "Dashboard - Rede Tradição",
					"page":       "dashboard",
					"ActivePage": "dashboard",
					"User": gin.H{
						"Role": c.GetString("userRole"),
						"Name": c.GetString("userName"),
					},
					"UserRole":  c.GetString("userRole"),
					"CSRFToken": csrfToken,
				})
			})

			// Configuração das rotas de permissões
			// Nota: Usando o router principal para que as rotas sejam registradas corretamente

			// Financeiro
			pages.GET("/financeiro", func(c *gin.Context) {
				c.HTML(http.StatusOK, "financeiro/painel.html", gin.H{
					"title":      "Financeiro - Rede Tradição",
					"page":       "financeiro",
					"ActivePage": "financeiro",
				})
			})

			// Relatórios
			pages.GET("/relatorios", func(c *gin.Context) {
				c.HTML(http.StatusOK, "relatorios/reports.html", gin.H{
					"title":      "Relatórios - Rede Tradição",
					"page":       "relatorios",
					"ActivePage": "relatorios",
				})
			})

			// Calendário
			pages.GET("/calendario", func(c *gin.Context) {
				// Buscar todas as ordens reais do banco
				orderRepo := repository.NewMaintenanceOrderRepository(db)
				orders, _, err := orderRepo.GetAll(c, nil, 0, "admin", 1, 200)
				if err != nil {
					c.HTML(500, "calendarios/calendar_flip.html", gin.H{
						"title":           "Calendário - Rede Tradição",
						"page":            "calendario",
						"ActivePage":      "calendario",
						"PriorityOrders":  []models.MaintenanceOrderDetailed{},
						"ScheduledOrders": []models.MaintenanceOrderDetailed{},
						"Error":           "Erro ao buscar ordens de manutenção: " + err.Error(),
					})
					return
				}
				// Filtrar ordens prioritárias (aguardando, atrasado, urgente)
				var priorityOrders []models.MaintenanceOrderDetailed
				var scheduledOrders []models.MaintenanceOrderDetailed
				for _, o := range orders {
					status := o.Status
					priority := o.Priority
					if status == "pending" || status == "delayed" || priority == "critical" || priority == "alta" || priority == "urgente" {
						priorityOrders = append(priorityOrders, o)
					} else if status == "approved" || status == "completed" || status == "scheduled" || status == "aprovada" || status == "concluida" || status == "agendada" {
						scheduledOrders = append(scheduledOrders, o)
					}
				}
				c.HTML(http.StatusOK, "calendarios/calendar_flip.html", gin.H{
					"title":           "Calendário - Rede Tradição",
					"page":            "calendario",
					"ActivePage":      "calendario",
					"PriorityOrders":  priorityOrders,
					"ScheduledOrders": scheduledOrders,
				})
			})

			// Rota para Ordens Técnicas (página específica para técnicos)
			pages.GET("/ordemtecnico", func(c *gin.Context) {
				// Obter mês e ano da query, ou usar o mês atual
				mes, _ := strconv.Atoi(c.DefaultQuery("mes", strconv.Itoa(int(time.Now().Month()))))
				ano, _ := strconv.Atoi(c.DefaultQuery("ano", strconv.Itoa(time.Now().Year())))

				// Validar mês e ano
				if mes < 1 || mes > 12 {
					mes = int(time.Now().Month())
				}
				if ano < 2000 || ano > 2100 {
					ano = time.Now().Year()
				}

				c.HTML(http.StatusOK, "tecnico/Ordemtecnico.html", gin.H{
					"title":      "Ordem do Técnico - Rede Tradição",
					"page":       "ordemtecnico",
					"ActivePage": "ordemtecnico",
					"Mes":        mes,
					"Ano":        ano,
					"User": gin.H{
						"ID":    c.GetInt("userID"),
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  c.GetString("userRole"),
					},
				})
			})

			// Rota para compatibilidade com ordemtecnica (redirecionamento)
			pages.GET("/ordemtecnica", func(c *gin.Context) {
				c.Redirect(http.StatusMovedPermanently, "/ordemtecnico")
			})

			// Minha Conta
			pages.GET("/minha-conta", func(c *gin.Context) {
				c.HTML(http.StatusOK, "minhaconta/minha_conta.html", gin.H{
					"title":      "Minha Conta - Rede Tradição",
					"page":       "minha-conta",
					"ActivePage": "minha-conta",
					"User": gin.H{
						"ID":    c.GetInt("userID"),
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  c.GetString("userRole"),
					},
					"now": time.Now(),
				})
			})

			// Minha Filial - Exclusiva para usuários do tipo filial
			pages.GET("/minha-filial", func(c *gin.Context) {
				// Log para depuração
				log.Printf("[MINHA-FILIAL] Acessando página minha-filial, userRole: %s", c.GetString("userRole"))

				// Verificar todos os valores no contexto
				log.Printf("[MINHA-FILIAL] Valores no contexto:")
				log.Printf("[MINHA-FILIAL] userID: %v", c.GetInt("userID"))
				log.Printf("[MINHA-FILIAL] userRole: %s", c.GetString("userRole"))
				log.Printf("[MINHA-FILIAL] branchID: %d", c.GetInt("branchID"))
				log.Printf("[MINHA-FILIAL] filialID: %d", c.GetInt("filialID"))
				log.Printf("[MINHA-FILIAL] stationID: %d", c.GetInt("stationID"))

				// Verificar se o usuário é do tipo filial
				userRole := c.GetString("userRole")
				log.Printf("[MINHA-FILIAL] Verificando userRole: '%s'", userRole)
				if userRole != "filial" && userRole != string(models.RoleFilial) {
					log.Printf("[MINHA-FILIAL] Usuário não é do tipo filial, redirecionando para /minha-conta")
					c.Redirect(http.StatusFound, "/minha-conta")
					return
				}

				// Obter o ID da filial - usar o ID do usuário como ID da filial
				userID := c.GetInt("userID")
				log.Printf("[MINHA-FILIAL] userID obtido do contexto: %d", userID)

				// Usar o ID do usuário como ID da filial para usuários do tipo filial
				branchID := userID
				log.Printf("[MINHA-FILIAL] Usando branchID = userID: %d", branchID)

				// Buscar informações da filial
				db := c.MustGet("db").(*gorm.DB)
				var branch models.Branch
				if err := db.First(&branch, branchID).Error; err != nil {
					log.Printf("[MINHA-FILIAL] Erro ao buscar filial %d: %v", branchID, err)
					c.HTML(http.StatusOK, "minhaconta/minha_filial.html", gin.H{
						"title":      "Minha Filial - Rede Tradição",
						"page":       "minha-filial",
						"ActivePage": "minha-filial",
						"Message":    "Erro ao buscar informações da filial",
					})
					return
				}

				c.HTML(http.StatusOK, "minhaconta/minha_filial.html", gin.H{
					"title":      "Minha Filial - Rede Tradição",
					"page":       "minha-filial",
					"ActivePage": "minha-filial",
					"User": gin.H{
						"ID":       c.GetInt("userID"),
						"Name":     c.GetString("userName"),
						"Email":    c.GetString("userEmail"),
						"Role":     c.GetString("userRole"),
						"BranchID": branchID,
					},
					"Branch": branch,
					"now":    time.Now(),
				})
			})

			// Editar Perfil
			pages.GET("/editar-perfil", func(c *gin.Context) {
				c.HTML(http.StatusOK, "minhaconta/editar_perfil.html", gin.H{
					"title":      "Editar Perfil - Rede Tradição",
					"page":       "editar-perfil",
					"ActivePage": "minha-conta",
					"User": gin.H{
						"ID":       c.GetInt("userID"),
						"Name":     c.GetString("userName"),
						"Email":    c.GetString("userEmail"),
						"Role":     c.GetString("userRole"),
						"Phone":    c.GetString("userPhone"),
						"Position": c.GetString("userPosition"),
						"Bio":      c.GetString("userBio"),
						"Preferences": gin.H{
							"EmailNotifications": true,
							"PushNotifications":  false,
							"SmsNotifications":   false,
						},
					},
				})
			})

			// Alterar Senha (página)
			pages.GET("/minha-conta/alterar-senha", func(c *gin.Context) {
				// Gerar token CSRF
				csrfToken := "token-simulado-123456" // Em produção, use um gerador de token real

				c.HTML(http.StatusOK, "minhaconta/alterar_senha.html", gin.H{
					"title":      "Alterar Senha - Rede Tradição",
					"page":       "alterar-senha",
					"ActivePage": "minha-conta",
					"csrfToken":  csrfToken,
					"User": gin.H{
						"ID":    c.GetInt("userID"),
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  c.GetString("userRole"),
					},
				})
			})

			// Configurações de Segurança (página)
			pages.GET("/security-settings", func(c *gin.Context) {
				c.HTML(http.StatusOK, "auth/security_settings.html", gin.H{
					"title":      "Configurações de Segurança - Rede Tradição",
					"page":       "security",
					"ActivePage": "security",
				})
			})

			// Alterar Senha (página alternativa)
			pages.GET("/change-password", func(c *gin.Context) {
				c.HTML(http.StatusOK, "auth/change_password.html", gin.H{
					"title":      "Alterar Senha - Rede Tradição",
					"page":       "change-password",
					"ActivePage": "change-password",
				})
			})

			// Rota para Manutenção de Ordens (página específica para técnicos)
			pages.GET("/manutencaoordem", func(c *gin.Context) {
				c.HTML(http.StatusOK, "tecnico/ManutencaoOrdem.html", gin.H{
					"title":      "Manutenção de Ordem - Rede Tradição",
					"page":       "manutencaoordem",
					"ActivePage": "manutencaoordem",
					"Mes":        int(time.Now().Month()),
					"Ano":        time.Now().Year(),
					"User": gin.H{
						"ID":    c.GetInt("userID"),
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  c.GetString("userRole"),
					},
				})
			})

			// Rota direta para calendario-flip (precisa de PageAccessMiddleware)
			pages.GET("/calendario-flip", func(c *gin.Context) {
				db := c.MustGet("db").(*gorm.DB)
				userIDStr := c.GetString("userID")
				userRole := c.GetString("userRole")
				userID, err := strconv.Atoi(userIDStr)
				if err != nil {
					c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
						"title":      "Calendário Flip - Rede Tradição",
						"page":       "calendario-flip",
						"ActivePage": "calendario-flip",
						"error":      "Falha ao obter dados de usuário",
					})
					return
				}

				// Preparar filtros baseados no perfil do usuário
				filters := map[string]interface{}{}
				// Admins, gerentes e financeiro podem ver todas as ordens sem filtros
				if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
					// Buscar equipamentos do técnico/prestador
					var techEquipments []models.TechnicianEquipment
					if err := db.Where("user_id = ?", userID).Find(&techEquipments).Error; err != nil {
						c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
							"title":      "Calendário Flip - Rede Tradição",
							"page":       "calendario-flip",
							"ActivePage": "calendario-flip",
							"error":      "Falha ao buscar equipamentos do técnico",
						})
						return
					}

					equipmentTypes := make([]string, 0, len(techEquipments))
					for _, te := range techEquipments {
						equipmentTypes = append(equipmentTypes, te.EquipmentType)
					}

					var user models.User
					if err := db.First(&user, userID).Error; err != nil {
						c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
							"title":      "Calendário Flip - Rede Tradição",
							"page":       "calendario-flip",
							"ActivePage": "calendario-flip",
							"error":      "Falha ao buscar dados do usuário",
						})
						return
					}

					filters["branch_id"] = user.BranchID
					if len(equipmentTypes) > 0 {
						filters["equipment_type"] = equipmentTypes
					}
				} else {
					log.Printf("[INFO] Usuário %s (ID: %d) com perfil %s acessando todas as ordens sem filtros",
						c.GetString("userEmail"), userID, userRole)
				}

				// Buscar ordens usando o repositório
				repo := repository.NewMaintenanceOrderRepository(db)
				allOrders, _, err := repo.GetAll(c, filters, int64(userID), userRole, 1, 200)
				if err != nil {
					c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
						"title":      "Calendário Flip - Rede Tradição",
						"page":       "calendario-flip",
						"ActivePage": "calendario-flip",
						"error":      "Falha ao buscar ordens de manutenção: " + err.Error(),
					})
					return
				}

				// Dividir em ordens prioritárias e programadas
				priorityOrders := []models.MaintenanceOrderDetailed{}
				scheduledOrders := []models.MaintenanceOrderDetailed{}

				for _, order := range allOrders {
					switch order.Status {
					case "aguardando", "atrasado", "urgente":
						priorityOrders = append(priorityOrders, order)
					case "aprovado", "concluido", "programado":
						scheduledOrders = append(scheduledOrders, order)
					}
				}

				log.Printf("[INFO] Encontradas %d ordens prioritárias e %d ordens programadas para o usuário %s",
					len(priorityOrders), len(scheduledOrders), c.GetString("userEmail"))

				c.HTML(http.StatusOK, "calendarios/calendar_flip.html", gin.H{
					"title":           "Calendário Flip - Rede Tradição",
					"page":            "calendario-flip",
					"ActivePage":      "calendario-flip",
					"PriorityOrders":  priorityOrders,
					"ScheduledOrders": scheduledOrders,
					"User": gin.H{
						"ID":    userID,
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  userRole,
					},
				})
			})

			// Configurar rotas da galeria (usar router principal, PageAccessMiddleware aplicado dentro da função ou nas rotas específicas)
			// routes.SetupGaleriaRoutes(pages) // Correção: Passar router
			// A lógica de permissão específica (página vs API) deve ser tratada dentro de SetupGaleriaRoutes

			// Página de Gerenciamento de Relações (apenas admin, gerente e financeiro)
			adminPages := pages.Group("")
			adminPages.Use(middleware.RoleMiddleware("admin", "gerente", "financeiro"))
			{
				adminPages.GET("/admin/link-management", func(c *gin.Context) {
					c.HTML(http.StatusOK, "link_management/link_management.html", gin.H{
						"title":      "Gerenciamento de Relações - Rede Tradição",
						"page":       "link-management",
						"ActivePage": "link-management",
						"User": gin.H{
							"ID":    c.GetInt("userID"),
							"Name":  c.GetString("userName"),
							"Email": c.GetString("userEmail"),
							"Role":  c.GetString("userRole"),
						},
					})
				})
			}

		} // Fim do grupo 'pages'

		// API Routes com verificação de permissão de API
		api := protected.Group("/api")
		api.Use(middleware.APIAccessMiddleware())
		{
			// Rotas de autenticação protegidas
			auth := api.Group("/auth")
			{
				auth.GET("/me", authHandler.GetCurrentUser)
				auth.GET("/2fa/setup", authHandler.SetupTOTP)
				auth.POST("/2fa/enable", authHandler.VerifyAndEnableTOTP)
				auth.POST("/2fa/disable", authHandler.DisableTOTP)
			}

			// Rotas de usuário
			user := api.Group("/user")
			{
				user.GET("/me", userAPIHandler.GetCurrentUser)
				user.PUT("/profile", userAPIHandler.UpdateUserProfile)
				user.POST("/change-password", userAPIHandler.ChangePassword)
				user.POST("/avatar", handlers.UploadAvatar)
			}

			// Rota para técnicos (para o calendário)
			api.GET("/users/technicians", userAPIHandler.GetTechnicians)

			// Rotas de equipamentos
			equipments := api.Group("/equipments")
			equipments.Use(middleware.StationMiddleware()) // Middleware específico para equipamentos
			{
				equipments.GET("", equipmentAPIHandler.GetAllEquipments)
				equipments.GET("/:id", equipmentAPIHandler.GetEquipmentByID)
				equipments.POST("", equipmentAPIHandler.CreateEquipment)
				equipments.PUT("/:id", equipmentAPIHandler.UpdateEquipment)
				equipments.DELETE("/:id", equipmentAPIHandler.DeleteEquipment)
				equipments.GET("/types", equipmentAPIHandler.GetEquipmentTypes)
				equipments.GET("/filial/:id", equipmentAPIHandler.GetEquipmentsByFilial)
			}

			// Rotas de métricas por filial
			branches := api.Group("/branches")
			branches.Use(branchAutoAssignmentMiddleware.BranchFilterMiddleware()) // Aplicar filtro por filial
			{
				branches.GET("/metrics", branchMetricsHandler.GetAllBranchesMetrics)
				branches.GET("/:id/metrics", branchMetricsHandler.GetBranchMetrics)
				branches.GET("/:id/equipment-metrics", branchMetricsHandler.GetEquipmentTypeMetrics)
			}

			// Rotas de métricas da filial do usuário
			myBranch := api.Group("/my-branch")
			myBranch.Use(branchAutoAssignmentMiddleware.BranchFilterMiddleware())
			{
				myBranch.GET("/metrics", branchMetricsHandler.GetMyBranchMetrics)
				myBranch.GET("/equipment-metrics", branchMetricsHandler.GetMyBranchEquipmentMetrics)
			}

			// Rotas de equipamentos problemáticos (apenas admin)
			api.GET("/equipment/problematic", branchMetricsHandler.GetTopProblematicEquipment)

			// Rota adicional para compatibilidade com o frontend
			equipment := api.Group("/equipment")
			equipment.Use(middleware.StationMiddleware())
			{
				equipment.GET("/types", equipmentAPIHandler.GetEquipmentTypes)
			}

			// Rota específica para tipos de equipamento removida - agora configurada via SetupEquipmentTypeRoutes

			// Inicializar serviço e handler de transferências de equipamentos
			// Comentado para evitar variáveis não utilizadas
			// transferRepo := repository.NewEquipmentTransferRepository(db)
			// transferService := services.NewEquipmentTransferService(transferRepo, equipmentRepo, branchRepo, notificationService)
			// Mover declaração para fora do grupo 'api'
			// transferHandler := handlers.NewEquipmentTransferHandler(transferService)

			// Configurar rotas de transferência de equipamentos (API - usar router principal)
			// routes.SetupEquipmentTransferRoutes(api, transferHandler) // Correção: Passar router
			// A lógica de permissão específica (página vs API) deve ser tratada dentro de SetupEquipmentTransferRoutes

			// Inicializar handler de notificações API (usar router principal)
			// Mover declaração para fora do grupo 'api'
			// notificationAPIHandler := handlers.NewNotificationAPIHandler(notificationService)
			// notificationAPIHandler.RegisterRoutes(api) // Correção: Passar router
			// A lógica de permissão específica (página vs API) deve ser tratada dentro de RegisterRoutes

			// Rotas de filiais (API) são configuradas em routes.SetupBranchRoutes

			// Rotas de manutenção (API)
			maintenance := api.Group("/maintenance")
			maintenance.Use(branchAutoAssignmentMiddleware.BranchFilterMiddleware()) // Aplicar filtro por filial
			{
				maintenance.GET("", maintenanceHandler.GetAll)
				// Aplicar vinculação automática na criação de ordens
				maintenance.POST("", branchAutoAssignmentMiddleware.AutoAssignBranch(), maintenanceHandler.Create)
			}
			api.GET("/dashboard/metrics", dashboardHandler.GetMetrics)

			// Rotas de ordens de manutenção (API) - REMOVIDAS
			// Funcionalidade consolidada no unified_order_handler
			// Usar /api/orders em vez de /api/maintenance-orders
			orders := api.Group("/maintenance-orders")
			{
				// Redirecionamentos para nova arquitetura
				orders.GET("", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders")
				})
				orders.GET("/metrics", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders/metrics")
				})
				orders.POST("/remove-test-orders", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})
			} // Fim do grupo 'orders' dentro de 'api'

			// Rotas para gerenciar atribuições de ordens a técnicos - REMOVIDAS
			// Funcionalidade consolidada no unified_order_handler
			techOrders := api.Group("/technician-orders")
			{
				// Redirecionamentos para nova arquitetura
				techOrders.POST("/assign", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})

				techOrders.DELETE("/technician/:technicianId/order/:orderId", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})

				// Redirecionar para endpoint unificado
				techOrders.GET("/technician/:technicianId", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders/technician")
				})

				techOrders.GET("/order/:orderId", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders/"+c.Param("orderId"))
				})

				techOrders.POST("/batch-assign", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})
			}

			// Configuração das rotas UNIFICADAS de ordens de serviço - API
			// SUBSTITUINDO todas as rotas duplicadas por endpoints únicos e consistentes
			// Criar handler unificado dentro do contexto da API
			unifiedHandler := handlers.NewUnifiedOrderHandler()

			// Registrar rotas unificadas diretamente no grupo API
			apiOrdersUnified := api.Group("/orders")
			{
				apiOrdersUnified.GET("", unifiedHandler.ListOrders)
				apiOrdersUnified.POST("", unifiedHandler.CreateOrder)
				apiOrdersUnified.GET("/:id", unifiedHandler.GetOrder)
				apiOrdersUnified.GET("/calendar", unifiedHandler.GetCalendarOrders)
				apiOrdersUnified.GET("/technician", unifiedHandler.GetTechnicianOrders)
				apiOrdersUnified.GET("/metrics", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"success": true,
						"message": "Métricas em desenvolvimento",
						"data": gin.H{
							"total":       0,
							"pending":     0,
							"in_progress": 0,
							"completed":   0,
						},
					})
				})
			}

		} // Fim do grupo 'api'

		// ROTAS WEB - Páginas HTML (fora do grupo API)
		// Estas rotas precisam estar no grupo 'pages' para funcionar corretamente
		webOrders := pages.Group("/orders")
		{
			// Página principal de ordens
			webOrders.GET("", func(c *gin.Context) {
				c.HTML(http.StatusOK, "ordens/orders_gallery_style.html", gin.H{
					"title":      "Ordens de Serviço - Rede Tradição",
					"page":       "orders",
					"ActivePage": "orders",
				})
			})

			// Página de criação de ordens - CORRIGIDA
			webOrders.GET("/create", func(c *gin.Context) {
				// Obter ID do usuário logado
				userID, exists := c.Get("userID")
				if !exists {
					c.Redirect(http.StatusFound, "/login")
					return
				}

				// Obter nome do usuário
				userName, _ := c.Get("userName")
				if userName == nil {
					userName = "Usuário"
				}

				// Obter ID da filial do usuário
				branchID, exists := c.Get("filialID")
				if !exists {
					branchID, exists = c.Get("branchID")
					if !exists {
						branchID = uint(0)
					}
				}

				// Obter nome da filial
				branchName, _ := c.Get("filialName")
				if branchName == nil {
					branchName = "Filial"
				}

				// Converter branchID para string para compatibilidade com template
				branchIDStr := fmt.Sprintf("%v", branchID)

				c.HTML(http.StatusOK, "ordens/create_order.html", gin.H{
					"title":      "Nova Ordem de Serviço - Rede Tradição",
					"page":       "orders_create",
					"ActivePage": "orders",
					"UserID":     userID,
					"UserName":   userName,
					"BranchID":   branchIDStr,
					"User": gin.H{
						"ID":   branchIDStr,
						"Name": branchName,
					},
					"BranchName": branchName,
				})
			})

			// Página de calendário
			webOrders.GET("/calendar", func(c *gin.Context) {
				c.HTML(http.StatusOK, "ordens/calendar.html", gin.H{
					"title":      "Calendário de Ordens - Rede Tradição",
					"page":       "orders_calendar",
					"ActivePage": "orders",
				})
			})

			// Página de detalhes da ordem
			webOrders.GET("/:id", func(c *gin.Context) {
				c.HTML(http.StatusOK, "ordens/order_detail.html", gin.H{
					"title":      "Detalhes da Ordem - Rede Tradição",
					"page":       "order_detail",
					"ActivePage": "orders",
					"OrderID":    c.Param("id"),
				})
			})
		}

	} // Fim do grupo 'protected'

	// Remover rotas duplicadas ou movidas que estavam fora do grupo 'protected' ou 'pages'
	// As rotas /manutencaoordem, /calendario-flip foram movidas para dentro do grupo 'pages'.
	// As chamadas para Setup...Routes foram ajustadas para usar o 'router' principal.

	// Configurar rotas da galeria com filtro de permissões (usar router principal)
	// Usar o serviço de filiais existente
	routes.SetupFilialFilteredRoutes(router, branchService, authService)

	// Configurar rotas de prestadoras (usar router principal)
	routes.SetupPrestadorasRoutes(router, authService)

	// Configurar rotas de técnicos
	technicianRepo := repository.NewTechnicianRepository(db)
	technicianService := services.NewTechnicianService(technicianRepo)
	technicianHandler := handlers.NewTechnicianHandler(userService, technicianService)
	routes.SetupTechnicianRoutes(router, technicianHandler, middleware.AuthMiddleware(authService))

	// Configurar rotas de tipos de equipamento
	routes.SetupEquipmentTypeRoutes(router, middleware.AuthMiddleware(authService))

	// REMOVIDO: Rotas de permissões temporariamente desabilitadas durante consolidação
	// routes.SetupPermissionRoutes(router, permissionsService, "data/permissions.yaml")

	// Configurar sistema de gerenciamento de vínculos
	serviceProviderRepo := repository.NewServiceProviderRepository(db)
	// Reutilizar o repositório de técnicos já criado acima

	// Criar uma nova instância do repositório de filiais
	branchRepoImpl := repository.NewBranchRepository(db)
	branchRepoAdapter := repository.NewBranchRepositoryAdapter(branchRepoImpl)

	// Inicializar repositório de gestores de prestadores
	serviceProviderManagerRepo := repository.NewGormServiceProviderManagerRepository()

	// Inicializar serviço de upload de arquivos (necessário para o ServiceProviderService)
	uploadService := services.NewFileUploadService("uploads")

	// Inicializar serviço de email (necessário para o ServiceProviderService)
	// Usando um serviço de email simulado para desenvolvimento
	emailService := services.NewSimulatedEmailService()

	// Inicializar o serviço de prestadores
	serviceProviderService := services.NewServiceProviderService(
		serviceProviderRepo,
		userRepo,
		serviceProviderManagerRepo,
		emailService,
		uploadService,
	)

	// Inicializar o handler de prestadores
	serviceProviderHandler := handlers.NewServiceProviderHandler(
		serviceProviderService,
		userService,
		uploadService,
		emailService,
	)

	// Configurar rotas de prestadores
	routes.SetupServiceProviderRoutes(router, serviceProviderHandler, middleware.AuthMiddleware(authService))

	// Inicializar serviço de gerenciamento de vínculos
	linkManagementService := services.NewLinkManagementService(
		db,
		branchRepoAdapter,
		technicianRepo,
		serviceProviderRepo,
	)
	linkManagementController := controllers.NewLinkManagementController(linkManagementService)
	routes.SetupLinkManagementRoutes(router, linkManagementController, nil, authService)

	// Configurar repositório e serviço de ordens
	ordemRepo := repository.NewOrdemRepository(db)
	ordemService := services.NewOrdemService(ordemRepo)

	// Configurar rotas de anexos
	routes.SetupAttachmentRoutes(router, attachmentHandler, authService)
	routes.SetupLegacyAttachmentRoutes(router, attachmentHandler, authService)

	// Configurar rotas da nova implementação de ordens
	ordemV2Handler := handlers.NewOrdemV2Handler(ordemService)
	routes.SetupOrdensRoutes(router, ordemV2Handler)

	// Configurar rotas de tutoriais
	routes.SetupTutorialRoutes(router, db, authService)

	// Registrar funções de template personalizadas
	for nome, funcao := range templates.TemplateFuncs() {
		router.SetFuncMap(template.FuncMap{
			nome: funcao,
		})
	}

	// Configurar tratamento de sinais para encerramento gracioso
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Iniciar o servidor em uma goroutine separada
	// Sempre usar a porta 8080, independentemente da configuração
	addr := fmt.Sprintf("0.0.0.0:%d", *port)
	log.Printf("Servidor iniciado em %s (http://%s)", addr, addr)

	// Configurar servidor HTTP
	srv := &http.Server{
		Addr:    addr,
		Handler: router,
	}

	// Iniciar o servidor em uma goroutine
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Erro ao iniciar servidor: %v", err)
		}
	}()

	// Aguardar sinal de interrupção
	<-quit
	log.Println("Encerrando servidor...")

	// Criar contexto com timeout para shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Tentar encerrar o servidor graciosamente
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Erro ao encerrar servidor: %v", err)
	}

	log.Println("Servidor encerrado com sucesso")
}

// Função auxiliar para formatar datas nullable
func formatNullableTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02")
}
