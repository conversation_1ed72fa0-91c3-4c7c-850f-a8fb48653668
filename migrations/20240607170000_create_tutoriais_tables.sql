-- <PERSON><PERSON><PERSON> da tabela de tutoriais
CREATE TABLE IF NOT EXISTS tutoriais (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    titulo VARCHAR(200) NOT NULL,
    descricao TEXT,
    conteudo TEXT NOT NULL,
    tipo VARCHAR(20) NOT NULL DEFAULT 'manutencao',
    nivel VARCHAR(20) NOT NULL DEFAULT 'iniciante',
    categoria VARCHAR(100),
    duracao_minutos INT DEFAULT 10,
    imagem_url VARCHAR(255),
    video_url VARCHAR(255),
    equipamento_id INT UNSIGNED,
    filial_id INT UNSIGNED,
    autor_id INT UNSIGNED NOT NULL,
    visualizacoes INT DEFAULT 0,
    avaliacao FLOAT DEFAULT 0,
    ativo BOOLEAN DEFAULT TRUE,
    tags TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    <PERSON>OREIG<PERSON> KEY (equipamento_id) REFERENCES equipamentos(id) ON DELETE SET NULL,
    FOREIGN KEY (filial_id) REFERENCES filiais(id) ON DELETE SET NULL,
    FOREIGN KEY (autor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_tutoriais_tipo (tipo),
    INDEX idx_tutoriais_nivel (nivel),
    INDEX idx_tutoriais_equipamento (equipamento_id),
    INDEX idx_tutoriais_filial (filial_id),
    INDEX idx_tutoriais_autor (autor_id),
    INDEX idx_tutoriais_ativo (ativo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Criação da tabela de tutoriais favoritos
CREATE TABLE IF NOT EXISTS tutoriais_favoritos (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT UNSIGNED NOT NULL,
    tutorial_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tutorial_id) REFERENCES tutoriais(id) ON DELETE CASCADE,
    UNIQUE KEY unq_usuario_tutorial (usuario_id, tutorial_id),
    INDEX idx_tutoriais_favoritos_usuario (usuario_id),
    INDEX idx_tutoriais_favoritos_tutorial (tutorial_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Criação da tabela de histórico de visualizações
CREATE TABLE IF NOT EXISTS tutoriais_historico_visualizacao (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT UNSIGNED NOT NULL,
    tutorial_id INT UNSIGNED NOT NULL,
    visualizado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tempo_assistido INT DEFAULT 0 COMMENT 'Tempo em segundos',
    FOREIGN KEY (usuario_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tutorial_id) REFERENCES tutoriais(id) ON DELETE CASCADE,
    INDEX idx_historico_usuario (usuario_id),
    INDEX idx_historico_tutorial (tutorial_id),
    INDEX idx_historico_data (visualizado_em)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
